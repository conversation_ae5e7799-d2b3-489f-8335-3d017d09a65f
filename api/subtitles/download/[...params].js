import youtubeDl from 'youtube-dl-exec';
import { consumeCredits, getUserFromRequest, logBandwidthUsage } from '../../../lib/credit-manager.js';

// Helper function to download and parse subtitle content
const downloadAndParseSubtitles = async (subtitleUrl) => {
  try {
    const response = await fetch(subtitleUrl);
    const content = await response.text();
    
    // Parse VTT format
    if (content.includes('WEBVTT')) {
      return parseVTTContent(content);
    }
    
    // Try to parse as JSON3 format (YouTube's format)
    try {
      const jsonData = JSON.parse(content);
      if (jsonData.events) {
        return parseJSON3Content(jsonData);
      }
    } catch (e) {
      // Not JSON, continue with VTT parsing
    }
    
    // Fallback to VTT parsing
    return parseVTTContent(content);
  } catch (error) {
    console.error('Error downloading/parsing subtitles:', error);
    return [];
  }
};

// Parse VTT subtitle format
const parseVTTContent = (content) => {
  const subtitles = [];
  const lines = content.split('\n');

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();

    // Look for timestamp lines (format: 00:00:00.000 --> 00:00:03.000)
    if (line.includes('-->')) {
      // Extract timestamp part (before any alignment/position attributes)
      const timestampMatch = line.match(/^([\d:.,]+)\s*-->\s*([\d:.,]+)/);
      if (!timestampMatch) continue;

      const startTime = timestampMatch[1];
      const endTime = timestampMatch[2];
      const start = parseVTTTime(startTime);
      const end = parseVTTTime(endTime);

      // Get the text lines that follow
      const textLines = [];
      const wordTimingLines = [];
      i++; // Move to next line after timestamp

      while (i < lines.length && !lines[i].includes('-->')) {
        const textLine = lines[i].trim();

        // Break on empty line (indicates end of this subtitle block)
        if (textLine === '') {
          break;
        }

        if (textLine) {
          // Check if this line contains word-level timing tags
          const hasWordTiming = textLine.includes('<c>') || textLine.match(/<\d+:\d+:\d+\.\d+>/);

          if (hasWordTiming) {
            // Store word-timing lines for potential extraction
            wordTimingLines.push(textLine);
          } else {
            // This is a clean text line, process it
            let cleanText = textLine
              // Remove any remaining VTT formatting tags
              .replace(/<[^>]*>/g, '')
              // Clean up extra spaces
              .replace(/\s+/g, ' ')
              .trim();

            if (cleanText && cleanText.length > 0) {
              textLines.push(cleanText);
            }
          }
        }
        i++;
      }
      i--; // Step back one since the loop will increment

      // If no clean text lines found, extract text from word-timing lines
      if (textLines.length === 0 && wordTimingLines.length > 0) {
        wordTimingLines.forEach(line => {
          let cleanText = line
            // Remove word-level timing tags like <00:00:01.920>
            .replace(/<\d+:\d+:\d+\.\d+>/g, '')
            // Remove <c> tags and other formatting
            .replace(/<[^>]*>/g, '')
            // Clean up extra spaces
            .replace(/\s+/g, ' ')
            .trim();

          if (cleanText && cleanText.length > 0) {
            textLines.push(cleanText);
          }
        });
      }

      // Only add subtitle if we have meaningful text and reasonable duration
      const duration = end - start;
      const finalText = textLines.join(' ').trim();

      if (finalText && finalText.length > 0 && duration > 0.1 && duration < 30) {
        subtitles.push({
          start,
          end,
          text: finalText
        });
      }
    }
  }

  // Merge consecutive subtitles with very short gaps (less than 0.2 seconds) and similar text
  const mergedSubtitles = [];
  for (let i = 0; i < subtitles.length; i++) {
    const current = subtitles[i];
    const next = subtitles[i + 1];

    // Only merge if gap is very small and texts are different (avoid duplicates)
    if (next &&
        (next.start - current.end) < 0.2 &&
        current.text &&
        next.text &&
        current.text !== next.text &&
        !next.text.includes(current.text)) {
      // Merge with next subtitle
      mergedSubtitles.push({
        start: current.start,
        end: next.end,
        text: `${current.text} ${next.text}`.trim()
      });
      i++; // Skip the next subtitle since we merged it
    } else {
      mergedSubtitles.push(current);
    }
  }

  return mergedSubtitles;
};

// Parse JSON3 subtitle format (YouTube's format)
const parseJSON3Content = (jsonData) => {
  const subtitles = [];
  
  if (jsonData.events) {
    for (const event of jsonData.events) {
      if (event.segs) {
        let text = '';
        for (const seg of event.segs) {
          if (seg.utf8) {
            text += seg.utf8;
          }
        }
        
        if (text.trim()) {
          subtitles.push({
            start: event.tStartMs / 1000,
            end: (event.tStartMs + event.dDurationMs) / 1000,
            text: text.trim()
          });
        }
      }
    }
  }
  
  return subtitles;
};

// Parse VTT time format to seconds
const parseVTTTime = (timeStr) => {
  // Handle both comma and dot as decimal separator
  const normalizedTime = timeStr.replace(',', '.');
  const parts = normalizedTime.split(':');

  if (parts.length === 3) {
    const hours = parseInt(parts[0]) || 0;
    const minutes = parseInt(parts[1]) || 0;
    const seconds = parseFloat(parts[2]) || 0;
    return hours * 3600 + minutes * 60 + seconds;
  } else if (parts.length === 2) {
    // Handle MM:SS.mmm format
    const minutes = parseInt(parts[0]) || 0;
    const seconds = parseFloat(parts[1]) || 0;
    return minutes * 60 + seconds;
  }

  return 0;
};

export default async (req, res) => {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'GET') {
    res.status(405).json({ error: 'Method not allowed' });
    return;
  }

  try {
    const { params } = req.query;
    
    // Log the incoming request details for debugging
    console.log('Request URL:', req.url);
    console.log('Query params:', req.query);
    console.log('Params:', params);

    // Handle the case where params might be a string instead of an array
    const paramArray = params.split('-') || [];
    if (paramArray.length < 2) {
      return res.status(400).json({ error: 'Invalid parameters. Expected format: videoId-langCode' });
    }
    // Extract videoId and langCode from the params
    const [videoId, langCode] = paramArray;
    
    if (!videoId) {
      return res.status(400).json({ error: 'Video ID is required' });
    }

    if (!langCode) {
      return res.status(400).json({ error: 'Language code is required' });
    }

    console.log(`Processing request for video: ${videoId}, language: ${langCode}`);

    // Authenticate user and consume credits
    const { userId, error: authError } = await getUserFromRequest(req);
    if (authError) {
      return res.status(401).json({ error: authError });
    }

    // Consume 1 credit for downloading subtitles
    const creditResult = await consumeCredits(userId, 1, 'Download subtitles');
    if (!creditResult.success) {
      return res.status(403).json({ error: creditResult.message });
    }

    // Get video info and extract subtitles using youtube-dl-exec
    const videoUrl = `https://www.youtube.com/watch?v=${videoId}`;

    // Configure youtube-dl-exec options
    const options = {
      dumpSingleJson: true,
      noCheckCertificates: true,
      noWarnings: true,
      preferFreeFormats: true,
      addHeader: [
        'referer:youtube.com',
        'user-agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      ]
    };

    // Add proxy if configured
    if (process.env.SOCKET_URL && process.env.SOCKET_ENABLED === 'true') {
      options.proxy = process.env.SOCKET_URL;
    }

    const videoInfo = await youtubeDl(videoUrl, options);

    let subtitles = [];
    let subtitleUrl = null;

    // Find the subtitle track for the requested language
    if (videoInfo.subtitles && videoInfo.subtitles[langCode]) {
      const subtitleTracks = videoInfo.subtitles[langCode];

      // Prefer VTT format, fallback to other formats
      const track = subtitleTracks.find(t => t.ext === 'vtt') || subtitleTracks[0];

      if (track) {
        subtitleUrl = track.url;
        console.log(`Found subtitles for ${langCode}: ${track.ext} format`);
      } else {
        return res.status(404).json({ error: 'Subtitles not found for the specified language' });
      }
    } else if (videoInfo.automatic_captions && videoInfo.automatic_captions[langCode]) {
      // Try automatic captions if manual subtitles not available
      const autoCaptions = videoInfo.automatic_captions[langCode];
      const track = autoCaptions.find(t => t.ext === 'vtt') || autoCaptions[0];

      if (track) {
        subtitleUrl = track.url;
        console.log(`Found auto-generated subtitles for ${langCode}: ${track.ext} format`);
      } else {
        return res.status(404).json({ error: 'Subtitles not found for the specified language' });
      }
    } else {
      return res.status(404).json({ error: 'No subtitles available for this video' });
    }

    // Download and parse subtitles
    if (subtitleUrl) {
      subtitles = await downloadAndParseSubtitles(subtitleUrl);
    }

    // Log bandwidth usage for analytics
    await logBandwidthUsage(userId, 'Download subtitles', 20, 1);

    res.status(200).json({
      videoId,
      title: videoInfo.title || 'YouTube Video',
      channel: videoInfo.uploader || videoInfo.channel || 'Unknown Channel',
      channelId: videoInfo.uploader_id || videoInfo.channel_id || null,
      channelUrl: videoInfo.uploader_url || videoInfo.channel_url || null,
      thumbnail: videoInfo.thumbnail || `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,
      duration: videoInfo.duration || null,
      uploadDate: videoInfo.upload_date || null,
      viewCount: videoInfo.view_count || null,
      language: langCode,
      subtitles,
      creditsUsed: 1,
      remainingCredits: creditResult.remainingCredits,
      formats: {
        vtt: generateVTT(subtitles, videoInfo, langCode),
        srt: generateSRT(subtitles),
        txt: generateTXT(subtitles, videoInfo, langCode),
        json: generateJSON(subtitles, videoInfo, langCode)
      }
    });

  } catch (error) {
    console.error('Error downloading subtitles:', error);
    res.status(500).json({
      error: 'Failed to download subtitles',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Generate VTT format
const generateVTT = (subtitles, videoInfo, langCode) => {
  let vtt = 'WEBVTT\n';
  vtt += `Kind: captions\n`;
  vtt += `Language: ${langCode}\n\n`;

  if (videoInfo.title) {
    vtt += `NOTE\n`;
    vtt += `Title: ${videoInfo.title}\n`;
    if (videoInfo.uploader || videoInfo.channel) {
      vtt += `Channel: ${videoInfo.uploader || videoInfo.channel}\n`;
    }
    vtt += `Video ID: ${videoInfo.id || 'Unknown'}\n\n`;
  }

  subtitles.forEach((subtitle) => {
    const startTime = formatVTTTime(subtitle.start);
    const endTime = formatVTTTime(subtitle.end);
    vtt += `${startTime} --> ${endTime}\n`;
    vtt += `${subtitle.text}\n\n`;
  });

  return vtt;
};

// Generate SRT format
const generateSRT = (subtitles) => {
  let srt = '';

  subtitles.forEach((subtitle, index) => {
    const startTime = formatSRTTime(subtitle.start);
    const endTime = formatSRTTime(subtitle.end);
    srt += `${index + 1}\n`;
    srt += `${startTime} --> ${endTime}\n`;
    srt += `${subtitle.text}\n\n`;
  });

  return srt;
};

// Generate TXT format with metadata
const generateTXT = (subtitles, videoInfo, langCode) => {
  let txt = '';

  // Add metadata header
  if (videoInfo.title) {
    txt += `Title: ${videoInfo.title}\n`;
  }
  if (videoInfo.uploader || videoInfo.channel) {
    txt += `Channel: ${videoInfo.uploader || videoInfo.channel}\n`;
  }
  txt += `Video ID: ${videoInfo.id || 'Unknown'}\n`;
  txt += `Language: ${langCode}\n`;
  if (videoInfo.duration) {
    txt += `Duration: ${Math.floor(videoInfo.duration / 60)}:${String(videoInfo.duration % 60).padStart(2, '0')}\n`;
  }
  txt += `\n--- Subtitles ---\n\n`;

  subtitles.forEach((subtitle) => {
    const startMin = Math.floor(subtitle.start / 60);
    const startSec = Math.floor(subtitle.start % 60);
    const endMin = Math.floor(subtitle.end / 60);
    const endSec = Math.floor(subtitle.end % 60);

    txt += `[${startMin}:${String(startSec).padStart(2, '0')} - ${endMin}:${String(endSec).padStart(2, '0')}] ${subtitle.text}\n`;
  });

  return txt;
};

// Generate JSON format
const generateJSON = (subtitles, videoInfo, langCode) => {
  return JSON.stringify({
    metadata: {
      title: videoInfo.title || 'YouTube Video',
      channel: videoInfo.uploader || videoInfo.channel || 'Unknown Channel',
      channelId: videoInfo.uploader_id || videoInfo.channel_id || null,
      channelUrl: videoInfo.uploader_url || videoInfo.channel_url || null,
      videoId: videoInfo.id || 'Unknown',
      language: langCode,
      duration: videoInfo.duration || null,
      uploadDate: videoInfo.upload_date || null,
      viewCount: videoInfo.view_count || null,
      thumbnail: videoInfo.thumbnail || null,
      extractedAt: new Date().toISOString(),
      totalSubtitles: subtitles.length
    },
    subtitles: subtitles.map((subtitle, index) => ({
      index: index + 1,
      start: subtitle.start,
      end: subtitle.end,
      duration: subtitle.end - subtitle.start,
      text: subtitle.text,
      startTime: formatVTTTime(subtitle.start),
      endTime: formatVTTTime(subtitle.end)
    }))
  }, null, 2);
};

// Format time for VTT (HH:MM:SS.mmm)
const formatVTTTime = (seconds) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${secs.toFixed(3).padStart(6, '0')}`;
};

// Format time for SRT (HH:MM:SS,mmm)
const formatSRTTime = (seconds) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${secs.toFixed(3).replace('.', ',').padStart(6, '0')}`;
};
