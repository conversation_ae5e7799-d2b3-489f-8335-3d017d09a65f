import { loadStripe } from '@stripe/stripe-js';

const stripePublishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;

if (!stripePublishableKey && typeof window !== 'undefined') {
  console.warn('Missing NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY environment variable');
}

export const stripePromise = stripePublishableKey ? loadStripe(stripePublishableKey) : null;

// Pricing configuration - Credit-based system
// Credit system: 1 credit = 1 API call
// - Getting available languages = 1 credit (optional)
// - Downloading subtitles = 1 credit
// - Default: Direct English download = 1 credit
export const PRICING_TIERS = {
  starter: {
    name: 'Starter',
    price: 10,
    priceId: process.env.NEXT_PUBLIC_STRIPE_STARTER_PRICE_ID || 'price_starter_monthly',
    features: [
      '50 credits (~50 subtitle downloads)',
      'VTT and TXT format downloads',
      'Manual and Auto-generated caption support',
      'English subtitle extraction',
      'Multi-language support',
      'Credits valid for 6 months'
    ],
    limits: {
      creditsPerMonth: 50,
      creditsPerAction: 1, // 1 credit per API call
      actionsPerExtraction: 1 // Direct download = 1 action (English default)
    },
    popular: false
  },
  pro: {
    name: 'Pro',
    price: 30,
    priceId: process.env.NEXT_PUBLIC_STRIPE_PRO_PRICE_ID || 'price_pro_monthly',
    features: [
      '200 credits (~200 subtitle downloads)',
      'VTT and TXT format downloads',
      'Manual and Auto-generated caption support',
      'English subtitle extraction',
      'Multi-language support',
      'Credits valid for 6 months'
    ],
    limits: {
      creditsPerMonth: 200,
      creditsPerAction: 1, // 1 credit per API call
      actionsPerExtraction: 1 // Direct download = 1 action
    },
    popular: true
  },
  creator: {
    name: 'Creator',
    price: 75,
    priceId: process.env.NEXT_PUBLIC_STRIPE_CREATOR_PRICE_ID || 'price_creator_monthly',
    features: [
      '600 credits (~600 subtitle downloads)',
      'VTT and TXT format downloads',
      'Manual and Auto-generated caption support',
      'English subtitle extraction',
      'Multi-language support',
      'Credits valid for 6 months'
    ],
    limits: {
      creditsPerMonth: 600,
      creditsPerAction: 1, // 1 credit per API call
      actionsPerExtraction: 1 // Direct download = 1 action
    },
    popular: false
  },
  // enterprise: {
  //   name: 'Enterprise',
  //   price: 150,
  //   priceId: process.env.NEXT_PUBLIC_STRIPE_ENTERPRISE_PRICE_ID || 'price_enterprise_monthly',
  //   features: [
  //     '1200 credits (~1200 subtitle downloads)',
  //     'All format downloads',
  //     'Manual and auto-generated captions',
  //     'Multi-language support',
  //     'Batch processing',
  //     'Download all languages option',
  //     'Custom integrations',
  //     'Dedicated support',
  //     'Credits valid for 6 months',
  //     'Volume discounts available'
  //   ],
  //   limits: {
  //     creditsPerMonth: 1200,
  //     creditsPerAction: 1, // 1 credit per API call
  //     actionsPerExtraction: 1 // Direct download = 1 action
  //   },
  //   popular: false
  // }
} as const;

export type PricingTier = keyof typeof PRICING_TIERS;
