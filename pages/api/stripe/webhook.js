import Stripe from 'stripe';
import { createServerSupabaseClient } from '../../../lib/supabase-server';

// Disable body parsing for webhook to get raw body
export const config = {
  api: {
    bodyParser: false,
  },
};

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);
const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

// Helper function to get raw body
const getRawBody = (req) => {
  return new Promise((resolve, reject) => {
    let data = '';
    req.on('data', chunk => {
      data += chunk;
    });
    req.on('end', () => {
      resolve(data);
    });
    req.on('error', err => {
      reject(err);
    });
  });
};

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const sig = req.headers['stripe-signature'];
  let event;

  try {
    const rawBody = await getRawBody(req);
    event = stripe.webhooks.constructEvent(rawBody, sig, webhookSecret);
  } catch (err) {
    console.error('Webhook signature verification failed:', err.message);
    return res.status(400).json({ error: 'Webhook signature verification failed' });
  }

  const supabase = createServerSupabaseClient();

  try {
    console.log(`Processing webhook event: ${event.type}`);

    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object;
        console.log('Checkout session completed:', {
          sessionId: session.id,
          mode: session.mode,
          userId: session.metadata?.user_id,
          customerId: session.customer
        });

        // Handle one-time credit pack purchase
        if (session.mode === 'payment') {
          await handleCreditPackPurchase(supabase, session);
        }
        break;
      }

      case 'payment_intent.succeeded': {
        const paymentIntent = event.data.object;
        console.log('Payment succeeded:', paymentIntent.id);
        // Additional handling if needed
        break;
      }

      case 'payment_intent.payment_failed': {
        const paymentIntent = event.data.object;
        console.log('Payment failed:', paymentIntent.id);
        await handlePaymentFailed(supabase, paymentIntent);
        break;
      }

      case 'customer.created': {
        const customer = event.data.object;
        console.log('Customer created:', customer.id);
        // Additional handling if needed
        break;
      }

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    res.status(200).json({ received: true });
  } catch (error) {
    console.error('Error processing webhook:', error);
    res.status(500).json({ error: 'Webhook processing failed' });
  }
}

async function handleCreditPackPurchase(supabase, session) {
  const userId = session.metadata.user_id;
  const paymentIntentId = session.payment_intent;
  const stripeCustomerId = session.customer;

  if (!userId) {
    console.error('No user_id found in session metadata');
    throw new Error('Missing user_id in session metadata');
  }

  if (!paymentIntentId) {
    console.error('No payment_intent found in session');
    throw new Error('Missing payment_intent in session');
  }

  // Get the line items to determine what was purchased
  const lineItems = await stripe.checkout.sessions.listLineItems(session.id);

  if (!lineItems.data || lineItems.data.length === 0) {
    console.error('No line items found in session');
    throw new Error('No line items found in session');
  }

  const priceId = lineItems.data[0].price.id;
  const tier = getTierFromPriceId(priceId);
  const { credits, amount } = getCreditPackDetails(tier);

  console.log(`Processing credit pack purchase: ${tier} - ${credits} credits for user ${userId}`);

  try {
    // Check if this payment has already been processed
    const { data: existingPurchase } = await supabase
      .from('credit_purchases')
      .select('id')
      .eq('stripe_payment_intent_id', paymentIntentId)
      .single();

    if (existingPurchase) {
      console.log(`Payment ${paymentIntentId} already processed, skipping`);
      return;
    }

    // First, insert the credit purchase record
    const { error: purchaseError } = await supabase
      .from('credit_purchases')
      .insert({
        user_id: userId,
        stripe_payment_intent_id: paymentIntentId,
        stripe_customer_id: stripeCustomerId,
        status: 'succeeded',
        tier: tier,
        credits_purchased: credits,
        amount_paid: amount,
        expires_at: new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000).toISOString() // 6 months from now
      });

    if (purchaseError) {
      console.error('Error inserting credit purchase:', purchaseError);
      throw purchaseError;
    }

    // Then, update user credits - check if user already has credits first
    const { data: existingCredits } = await supabase
      .from('user_credits')
      .select('total_credits, used_credits')
      .eq('user_id', userId)
      .single();

    if (existingCredits) {
      // Update existing record by adding new credits
      const { error: updateError } = await supabase
        .from('user_credits')
        .update({
          total_credits: existingCredits.total_credits + credits,
          last_purchase_date: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId);

      if (updateError) {
        console.error('Error updating user credits:', updateError);
        throw updateError;
      }

      console.log(`Updated user ${userId}: ${existingCredits.total_credits} + ${credits} = ${existingCredits.total_credits + credits} total credits`);
    } else {
      // Insert new record for first-time purchase
      const { error: insertError } = await supabase
        .from('user_credits')
        .insert({
          user_id: userId,
          total_credits: credits,
          used_credits: 0,
          last_purchase_date: new Date().toISOString()
        });

      if (insertError) {
        console.error('Error inserting user credits:', insertError);
        throw insertError;
      }

      console.log(`Created new credit record for user ${userId}: ${credits} total credits`);
    }

    // Initialize usage stats if they don't exist
    await supabase
      .from('usage_stats')
      .upsert({
        user_id: userId,
        credits_used_this_month: 0,
        videos_extracted_this_month: 0,
        last_reset_date: new Date().toISOString()
      }, { onConflict: 'user_id' });

    console.log(`Successfully added ${credits} credits to user ${userId}`);
  } catch (error) {
    console.error('Error processing credit pack purchase:', error);
    throw error;
  }
}

async function handlePaymentFailed(supabase, paymentIntent) {
  console.log('Handling payment failure for:', paymentIntent.id);

  // Update credit purchase status to failed if it exists
  const { error } = await supabase
    .from('credit_purchases')
    .update({
      status: 'failed',
      updated_at: new Date().toISOString()
    })
    .eq('stripe_payment_intent_id', paymentIntent.id);

  if (error) {
    console.error('Error updating failed payment:', error);
  }
}

function getTierFromPriceId(priceId) {
  const priceMapping = {
    [process.env.NEXT_PUBLIC_STRIPE_STARTER_PRICE_ID]: 'starter',
    [process.env.NEXT_PUBLIC_STRIPE_PRO_PRICE_ID]: 'pro',
    [process.env.NEXT_PUBLIC_STRIPE_CREATOR_PRICE_ID]: 'creator'
  };

  return priceMapping[priceId] || 'starter';
}

function getCreditPackDetails(tier) {
  const creditPacks = {
    starter: { credits: 50, amount: 10 },
    pro: { credits: 200, amount: 30 },
    creator: { credits: 600, amount: 75 }
  };

  return creditPacks[tier] || creditPacks.starter;
}


