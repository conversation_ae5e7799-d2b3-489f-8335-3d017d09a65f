import { createServerSupabaseClient } from '../../../lib/supabase-server';

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { userId } = req.query;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    const supabase = createServerSupabaseClient();

    console.log(`Debug: Testing credits for user ${userId}`);

    // Get user credits
    const { data: userCredits, error: creditsError } = await supabase
      .from('user_credits')
      .select('*')
      .eq('user_id', userId)
      .single();

    // Get credit purchases
    const { data: purchases, error: purchasesError } = await supabase
      .from('credit_purchases')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    // Get usage stats
    const { data: usage, error: usageError } = await supabase
      .from('usage_stats')
      .select('*')
      .eq('user_id', userId)
      .single();

    // Get user info
    const { data: userInfo, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    // Calculate available credits
    const availableCredits = userCredits ? userCredits.total_credits - userCredits.used_credits : 0;
    const totalPurchased = purchases.reduce((sum, purchase) => sum + purchase.credits_purchased, 0);

    res.status(200).json({
      userId,
      summary: {
        totalCredits: userCredits?.total_credits || 0,
        usedCredits: userCredits?.used_credits || 0,
        availableCredits: availableCredits,
        totalPurchased: totalPurchased,
        purchaseCount: purchases.length,
        lastPurchase: userCredits?.last_purchase_date || null
      },
      userInfo: userInfo || null,
      userCredits: userCredits || null,
      purchases: purchases || [],
      usage: usage || null,
      errors: {
        creditsError: creditsError?.code === 'PGRST116' ? null : creditsError,
        purchasesError: purchasesError?.code === 'PGRST116' ? null : purchasesError,
        usageError: usageError?.code === 'PGRST116' ? null : usageError,
        userError: userError?.code === 'PGRST116' ? null : userError
      }
    });

  } catch (error) {
    console.error('Error in debug test credits:', error);
    res.status(500).json({ error: 'Internal server error', details: error.message });
  }
}
