import { createServerSupabaseClient } from '../../../lib/supabase-server';

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { userId, tier = 'starter' } = req.body;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Simulate a successful Stripe checkout session
    const mockSession = {
      id: `cs_test_${Date.now()}`,
      customer: `cus_test_${userId}`,
      payment_intent: `pi_test_${Date.now()}`,
      mode: 'payment',
      metadata: {
        user_id: userId
      }
    };

    // Simulate line items
    const mockLineItems = {
      data: [{
        price: {
          id: getTestPriceId(tier)
        }
      }]
    };

    const supabase = createServerSupabaseClient();

    // Simulate the webhook processing
    const result = await simulateWebhookProcessing(supabase, mockSession, mockLineItems);

    res.status(200).json({
      success: true,
      message: 'Webhook simulation completed',
      result: result,
      mockSession: mockSession
    });

  } catch (error) {
    console.error('Error in webhook simulation:', error);
    res.status(500).json({ error: 'Webhook simulation failed', details: error.message });
  }
}

async function simulateWebhookProcessing(supabase, session, lineItems) {
  const userId = session.metadata.user_id;
  const paymentIntentId = session.payment_intent;
  const stripeCustomerId = session.customer;

  const priceId = lineItems.data[0].price.id;
  const tier = getTierFromPriceId(priceId);
  const { credits, amount } = getCreditPackDetails(tier);

  console.log(`Simulating credit pack purchase: ${tier} - ${credits} credits for user ${userId}`);

  // Check if this payment has already been processed
  const { data: existingPurchase } = await supabase
    .from('credit_purchases')
    .select('id')
    .eq('stripe_payment_intent_id', paymentIntentId)
    .single();

  if (existingPurchase) {
    return { message: `Payment ${paymentIntentId} already processed`, skipped: true };
  }

  // Insert the credit purchase record
  const { error: purchaseError } = await supabase
    .from('credit_purchases')
    .insert({
      user_id: userId,
      stripe_payment_intent_id: paymentIntentId,
      stripe_customer_id: stripeCustomerId,
      status: 'succeeded',
      tier: tier,
      credits_purchased: credits,
      amount_paid: amount,
      expires_at: new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000).toISOString()
    });

  if (purchaseError) {
    throw new Error(`Error inserting credit purchase: ${purchaseError.message}`);
  }

  // Update user credits
  const { data: existingCredits } = await supabase
    .from('user_credits')
    .select('total_credits, used_credits')
    .eq('user_id', userId)
    .single();

  if (existingCredits) {
    const { error: updateError } = await supabase
      .from('user_credits')
      .update({
        total_credits: existingCredits.total_credits + credits,
        last_purchase_date: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId);

    if (updateError) {
      throw new Error(`Error updating user credits: ${updateError.message}`);
    }

    return {
      message: `Updated user ${userId}: ${existingCredits.total_credits} + ${credits} = ${existingCredits.total_credits + credits} total credits`,
      previousCredits: existingCredits.total_credits,
      addedCredits: credits,
      newTotal: existingCredits.total_credits + credits
    };
  } else {
    const { error: insertError } = await supabase
      .from('user_credits')
      .insert({
        user_id: userId,
        total_credits: credits,
        used_credits: 0,
        last_purchase_date: new Date().toISOString()
      });

    if (insertError) {
      throw new Error(`Error inserting user credits: ${insertError.message}`);
    }

    return {
      message: `Created new credit record for user ${userId}: ${credits} total credits`,
      previousCredits: 0,
      addedCredits: credits,
      newTotal: credits
    };
  }
}

function getTestPriceId(tier) {
  const testPriceIds = {
    starter: 'price_test_starter',
    pro: 'price_test_pro',
    creator: 'price_test_creator'
  };
  return testPriceIds[tier] || testPriceIds.starter;
}

function getTierFromPriceId(priceId) {
  if (priceId.includes('starter')) return 'starter';
  if (priceId.includes('pro')) return 'pro';
  if (priceId.includes('creator')) return 'creator';
  return 'starter';
}

function getCreditPackDetails(tier) {
  const creditPacks = {
    starter: { credits: 50, amount: 10 },
    pro: { credits: 200, amount: 30 },
    creator: { credits: 600, amount: 75 }
  };
  return creditPacks[tier] || creditPacks.starter;
}
