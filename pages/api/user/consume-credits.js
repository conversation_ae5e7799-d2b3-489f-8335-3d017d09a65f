import { createServerSupabaseClient } from '../../../lib/supabase-server';

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { credits = 1 } = req.body;

    // Get user from auth header
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      return res.status(401).json({ error: 'Authorization required' });
    }

    const supabase = createServerSupabaseClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser(
      authHeader.replace('Bearer ', '')
    );

    if (authError || !user) {
      return res.status(401).json({ error: 'Invalid authorization' });
    }

    // Check if user has enough credits first
    const { data: userCredits, error: creditsError } = await supabase
      .from('user_credits')
      .select('available_credits')
      .eq('user_id', user.id)
      .single();

    if (creditsError || !userCredits || userCredits.available_credits < credits) {
      return res.status(403).json({ 
        error: `Insufficient credits. You need ${credits} credit(s).`,
        availableCredits: userCredits?.available_credits || 0
      });
    }

    // Consume credits using the database function
    const { data, error: consumeError } = await supabase.rpc('consume_credits', {
      p_user_id: user.id,
      p_credits: credits
    });

    if (consumeError) {
      console.error('Error consuming credits:', consumeError);
      return res.status(500).json({ error: 'Failed to consume credits' });
    }

    if (!data) {
      return res.status(403).json({ error: 'Insufficient credits' });
    }

    res.status(200).json({ 
      success: true, 
      creditsConsumed: credits,
      message: `${credits} credit(s) consumed successfully`
    });

  } catch (error) {
    console.error('Error consuming credits:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
