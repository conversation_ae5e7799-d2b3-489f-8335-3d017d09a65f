import { createServerSupabaseClient } from '../../../lib/supabase-server';

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Get user from auth header
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      return res.status(401).json({ error: 'Authorization required' });
    }

    const supabase = createServerSupabaseClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser(
      authHeader.replace('Bearer ', '')
    );

    if (authError || !user) {
      return res.status(401).json({ error: 'Invalid authorization' });
    }

    // Fetch user credits
    const { data: creditsData, error: creditsError } = await supabase
      .from('user_credits')
      .select('*')
      .eq('user_id', user.id)
      .single();

    // Fetch usage stats
    const { data: usageData, error: usageError } = await supabase
      .from('usage_stats')
      .select('*')
      .eq('user_id', user.id)
      .single();

    res.status(200).json({
      userCredits: creditsData || null,
      usage: usageData || null,
      creditsError: creditsError?.code === 'PGRST116' ? null : creditsError,
      usageError: usageError?.code === 'PGRST116' ? null : usageError
    });

  } catch (error) {
    console.error('Error fetching credit data:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
