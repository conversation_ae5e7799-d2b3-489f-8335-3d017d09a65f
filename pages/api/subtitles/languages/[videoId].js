import youtubeDl from 'youtube-dl-exec';


// Helper function to get language name from code
const getLanguageName = (code) => {
  const languageMap = {
    'en': 'English',
    'es': 'Spanish',
    'fr': 'French',
    'de': 'German',
    'it': 'Italian',
    'pt': 'Portuguese',
    'ru': 'Russian',
    'ja': 'Japanese',
    'ko': 'Korean',
    'zh': 'Chinese',
    'ar': 'Arabic',
    'hi': 'Hindi',
    'tr': 'Turkish',
    'pl': 'Polish',
    'nl': 'Dutch',
    'sv': 'Swedish',
    'da': 'Danish',
    'no': 'Norwegian',
    'fi': 'Finnish',
    'cs': 'Czech',
    'hu': 'Hungarian',
    'ro': 'Romanian',
    'bg': 'Bulgarian',
    'hr': 'Croatian',
    'sk': 'Slovak',
    'sl': 'Slovenian',
    'et': 'Estonian',
    'lv': 'Latvian',
    'lt': 'Lithuanian',
    'uk': 'Ukrainian',
    'el': 'Greek',
    'he': 'Hebrew',
    'th': 'Thai',
    'vi': 'Vietnamese',
    'id': 'Indonesian',
    'ms': 'Malay',
    'tl': 'Filipino',
    'sw': 'Swahili',
    'af': 'Afrikaans'
  };
  
  return languageMap[code] || code.toUpperCase();
};

// Proxy configuration for youtube-dl-exec will be handled in options
export default async (req, res) => {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'GET') {
    res.status(405).json({ error: 'Method not allowed' });
    return;
  }

  try {
    // Consume credits first
    const authHeader = req.headers.authorization;
    if (authHeader) {
      try {
        const consumeResponse = await fetch(`${req.headers.host?.includes('localhost') ? 'http' : 'https'}://${req.headers.host}/api/user/consume-credits`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': authHeader
          },
          body: JSON.stringify({ credits: 1 })
        });

        if (!consumeResponse.ok) {
          const errorData = await consumeResponse.json();
          return res.status(consumeResponse.status).json(errorData);
        }
      } catch (creditError) {
        console.error('Error consuming credits:', creditError);
        return res.status(500).json({ error: 'Failed to process credit consumption' });
      }
    }

    const { videoId } = req.query;

    if (!videoId) {
      return res.status(400).json({ error: 'Video ID is required' });
    }

    console.log(`Fetching subtitle languages for video: ${videoId}`);

    // Get video info including available subtitles using youtube-dl-exec
    const videoUrl = `https://www.youtube.com/watch?v=${videoId}`;

    // Configure youtube-dl-exec options
    const options = {
      dumpSingleJson: true,
      noCheckCertificates: true,
      noWarnings: true,
      preferFreeFormats: true,
      addHeader: [
        'referer:youtube.com',
        'user-agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      ]
    };

    // Add proxy if configured
    if (process.env.SOCKET_URL && process.env.SOCKET_ENABLED === 'true') {
      options.proxy = process.env.SOCKET_URL;
    }

    const videoInfo = await youtubeDl(videoUrl, options);

    const availableLanguages = [];

    // Get available subtitle tracks (manual subtitles)
    if (videoInfo.subtitles) {
      Object.keys(videoInfo.subtitles).forEach(langCode => {
        const langName = getLanguageName(langCode);
        availableLanguages.push({
          code: langCode,
          name: langName,
          isAutoGenerated: false
        });
      });
    }

    // Get available automatic captions
    if (videoInfo.automatic_captions) {
      Object.keys(videoInfo.automatic_captions).forEach(langCode => {
        // Only add if not already present from manual subtitles
        if (!availableLanguages.find(lang => lang.code === langCode)) {
          const langName = getLanguageName(langCode);
          availableLanguages.push({
            code: langCode,
            name: langName,
            isAutoGenerated: true
          });
        }
      });
    }

    if (availableLanguages.length === 0) {
      return res.status(404).json({ error: 'No subtitles available for this video' });
    }

    // Sort languages: English first, then alphabetically
    availableLanguages.sort((a, b) => {
      if (a.code === 'en') return -1;
      if (b.code === 'en') return 1;
      return a.name.localeCompare(b.name);
    });

    res.status(200).json({
      videoId,
      title: videoInfo.title || 'YouTube Video',
      channel: videoInfo.uploader || videoInfo.channel || 'Unknown Channel',
      channelId: videoInfo.uploader_id || videoInfo.channel_id || null,
      channelUrl: videoInfo.uploader_url || videoInfo.channel_url || null,
      thumbnail: videoInfo.thumbnail || `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,
      duration: videoInfo.duration || null,
      uploadDate: videoInfo.upload_date || null,
      viewCount: videoInfo.view_count || null,
      languages: availableLanguages
    });

  } catch (error) {
    console.error('Error fetching subtitle languages:', error);
    res.status(500).json({
      error: 'Failed to fetch subtitle languages',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};
