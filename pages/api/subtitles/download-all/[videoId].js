import { createServerSupabaseClient } from '../../../../lib/supabase';

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { videoId } = req.query;

    if (!videoId) {
      return res.status(400).json({ error: 'Video ID is required' });
    }

    // Get user from auth header
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      return res.status(401).json({ error: 'Authorization required' });
    }

    const supabase = createServerSupabaseClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser(
      authHeader.replace('Bearer ', '')
    );

    if (authError || !user) {
      return res.status(401).json({ error: 'Invalid authorization' });
    }

    // Check if user has enough credits (1 credit for all languages)
    const { data: userCredits, error: creditsError } = await supabase
      .from('user_credits')
      .select('available_credits')
      .eq('user_id', user.id)
      .single();

    if (creditsError || !userCredits || userCredits.available_credits < 1) {
      return res.status(403).json({ 
        error: 'Insufficient credits. You need 1 credit to download all languages.' 
      });
    }

    // First get available languages
    const languagesResponse = await fetch(`${req.headers.origin || 'http://localhost:3001'}/api/subtitles/languages/${videoId}`, {
      headers: {
        'Authorization': authHeader
      }
    });

    if (!languagesResponse.ok) {
      return res.status(400).json({ error: 'Failed to get available languages' });
    }

    const languagesData = await languagesResponse.json();
    const languages = languagesData.languages || [];

    if (languages.length === 0) {
      return res.status(404).json({ error: 'No languages available for this video' });
    }

    // Download all languages
    const allSubtitles = {};
    const downloadPromises = languages.map(async (lang) => {
      try {
        const response = await fetch(`${req.headers.origin || 'http://localhost:3001'}/api/subtitles/download/${videoId}-${lang.code}`, {
          headers: {
            'Authorization': authHeader
          }
        });

        if (response.ok) {
          const data = await response.json();
          allSubtitles[lang.code] = {
            language: lang.name,
            subtitles: data.subtitles || [],
            isAutoGenerated: lang.isAutoGenerated
          };
        }
      } catch (error) {
        console.error(`Failed to download ${lang.code}:`, error);
      }
    });

    await Promise.all(downloadPromises);

    // Consume 1 credit for all languages download
    const { error: consumeError } = await supabase.rpc('consume_credits', {
      p_user_id: user.id,
      p_credits: 1
    });

    if (consumeError) {
      console.error('Error consuming credits:', consumeError);
      return res.status(500).json({ error: 'Failed to process credit consumption' });
    }

    // Create ZIP-like structure (simplified for this example)
    // In a real implementation, you'd use a ZIP library
    const zipContent = {
      videoTitle: languagesData.title || 'Unknown',
      videoId: videoId,
      downloadedAt: new Date().toISOString(),
      languages: allSubtitles,
      totalLanguages: Object.keys(allSubtitles).length
    };

    res.status(200).json({
      success: true,
      zipData: JSON.stringify(zipContent, null, 2),
      totalLanguages: Object.keys(allSubtitles).length,
      message: `Downloaded ${Object.keys(allSubtitles).length} languages`
    });

  } catch (error) {
    console.error('Error downloading all languages:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
