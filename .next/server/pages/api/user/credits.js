"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/user/credits";
exports.ids = ["pages/api/user/credits"];
exports.modules = {

/***/ "(api-node)/./lib/supabase-server.js":
/*!********************************!*\
  !*** ./lib/supabase-server.js ***!
  \********************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServerSupabaseClient: () => (/* binding */ createServerSupabaseClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n\nfunction createServerSupabaseClient() {\n    const supabaseUrl = \"https://vsaxiialrhpqdlcpmetn.supabase.co\";\n    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    if (!supabaseUrl || !supabaseServiceKey) {\n        throw new Error('Missing Supabase environment variables');\n    }\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaS1ub2RlKS8uL2xpYi9zdXBhYmFzZS1zZXJ2ZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBcUQ7QUFFOUMsU0FBU0M7SUFDZCxNQUFNQyxjQUFjQywwQ0FBb0M7SUFDeEQsTUFBTUcscUJBQXFCSCxRQUFRQyxHQUFHLENBQUNHLHlCQUF5QjtJQUVoRSxJQUFJLENBQUNMLGVBQWUsQ0FBQ0ksb0JBQW9CO1FBQ3ZDLE1BQU0sSUFBSUUsTUFBTTtJQUNsQjtJQUVBLE9BQU9SLG1FQUFZQSxDQUFDRSxhQUFhSSxvQkFBb0I7UUFDbkRHLE1BQU07WUFDSkMsa0JBQWtCO1lBQ2xCQyxnQkFBZ0I7UUFDbEI7SUFDRjtBQUNGIiwic291cmNlcyI6WyIvVXNlcnMvZGluZXNocy9Eb2N1bWVudHMvRGV2L1Byb2plY3RzL1lUU3VidGl0bGVFeHRyYWN0b3IvWVRTdWJ0aXRsZUV4dHJhY3Rvci9saWIvc3VwYWJhc2Utc2VydmVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUNsaWVudCB9IGZyb20gJ0BzdXBhYmFzZS9zdXBhYmFzZS1qcyc7XG5cbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVTZXJ2ZXJTdXBhYmFzZUNsaWVudCgpIHtcbiAgY29uc3Qgc3VwYWJhc2VVcmwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkw7XG4gIGNvbnN0IHN1cGFiYXNlU2VydmljZUtleSA9IHByb2Nlc3MuZW52LlNVUEFCQVNFX1NFUlZJQ0VfUk9MRV9LRVk7XG5cbiAgaWYgKCFzdXBhYmFzZVVybCB8fCAhc3VwYWJhc2VTZXJ2aWNlS2V5KSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdNaXNzaW5nIFN1cGFiYXNlIGVudmlyb25tZW50IHZhcmlhYmxlcycpO1xuICB9XG5cbiAgcmV0dXJuIGNyZWF0ZUNsaWVudChzdXBhYmFzZVVybCwgc3VwYWJhc2VTZXJ2aWNlS2V5LCB7XG4gICAgYXV0aDoge1xuICAgICAgYXV0b1JlZnJlc2hUb2tlbjogZmFsc2UsXG4gICAgICBwZXJzaXN0U2Vzc2lvbjogZmFsc2VcbiAgICB9XG4gIH0pO1xufVxuIl0sIm5hbWVzIjpbImNyZWF0ZUNsaWVudCIsImNyZWF0ZVNlcnZlclN1cGFiYXNlQ2xpZW50Iiwic3VwYWJhc2VVcmwiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIiwic3VwYWJhc2VTZXJ2aWNlS2V5IiwiU1VQQUJBU0VfU0VSVklDRV9ST0xFX0tFWSIsIkVycm9yIiwiYXV0aCIsImF1dG9SZWZyZXNoVG9rZW4iLCJwZXJzaXN0U2Vzc2lvbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api-node)/./lib/supabase-server.js\n");

/***/ }),

/***/ "(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fuser%2Fcredits&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fuser%2Fcredits.js&middlewareConfigBase64=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fuser%2Fcredits&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fuser%2Fcredits.js&middlewareConfigBase64=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/./node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_user_credits_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages/api/user/credits.js */ \"(api-node)/./pages/api/user/credits.js\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_user_credits_js__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_user_credits_js__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/user/credits\",\n        pathname: \"/api/user/credits\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_user_credits_js__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaS1ub2RlKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtcm91dGUtbG9hZGVyL2luZGV4LmpzP2tpbmQ9UEFHRVNfQVBJJnBhZ2U9JTJGYXBpJTJGdXNlciUyRmNyZWRpdHMmcHJlZmVycmVkUmVnaW9uPSZhYnNvbHV0ZVBhZ2VQYXRoPS4lMkZwYWdlcyUyRmFwaSUyRnVzZXIlMkZjcmVkaXRzLmpzJm1pZGRsZXdhcmVDb25maWdCYXNlNjQ9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUErRjtBQUN2QztBQUNFO0FBQzFEO0FBQ3dEO0FBQ3hEO0FBQ0EsaUVBQWUsd0VBQUssQ0FBQyx1REFBUSxZQUFZLEVBQUM7QUFDMUM7QUFDTyxlQUFlLHdFQUFLLENBQUMsdURBQVE7QUFDcEM7QUFDTyx3QkFBd0IseUdBQW1CO0FBQ2xEO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLFlBQVk7QUFDWixDQUFDOztBQUVEIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUGFnZXNBUElSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvcGFnZXMtYXBpL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgaG9pc3QgfSBmcm9tIFwibmV4dC9kaXN0L2J1aWxkL3RlbXBsYXRlcy9oZWxwZXJzXCI7XG4vLyBJbXBvcnQgdGhlIHVzZXJsYW5kIGNvZGUuXG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiLi9wYWdlcy9hcGkvdXNlci9jcmVkaXRzLmpzXCI7XG4vLyBSZS1leHBvcnQgdGhlIGhhbmRsZXIgKHNob3VsZCBiZSB0aGUgZGVmYXVsdCBleHBvcnQpLlxuZXhwb3J0IGRlZmF1bHQgaG9pc3QodXNlcmxhbmQsICdkZWZhdWx0Jyk7XG4vLyBSZS1leHBvcnQgY29uZmlnLlxuZXhwb3J0IGNvbnN0IGNvbmZpZyA9IGhvaXN0KHVzZXJsYW5kLCAnY29uZmlnJyk7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBQYWdlc0FQSVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5QQUdFU19BUEksXG4gICAgICAgIHBhZ2U6IFwiL2FwaS91c2VyL2NyZWRpdHNcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS91c2VyL2NyZWRpdHNcIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiAnJyxcbiAgICAgICAgZmlsZW5hbWU6ICcnXG4gICAgfSxcbiAgICB1c2VybGFuZFxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBhZ2VzLWFwaS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fuser%2Fcredits&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fuser%2Fcredits.js&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/./pages/api/user/credits.js":
/*!***********************************!*\
  !*** ./pages/api/user/credits.js ***!
  \***********************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../lib/supabase-server */ \"(api-node)/./lib/supabase-server.js\");\n\nasync function handler(req, res) {\n    // Set CORS headers\n    res.setHeader('Access-Control-Allow-Origin', '*');\n    res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');\n    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');\n    if (req.method === 'OPTIONS') {\n        return res.status(200).end();\n    }\n    if (req.method !== 'GET') {\n        return res.status(405).json({\n            error: 'Method not allowed'\n        });\n    }\n    try {\n        // Get user from auth header\n        const authHeader = req.headers.authorization;\n        if (!authHeader) {\n            return res.status(401).json({\n                error: 'Authorization required'\n            });\n        }\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n        const { data: { user }, error: authError } = await supabase.auth.getUser(authHeader.replace('Bearer ', ''));\n        if (authError || !user) {\n            return res.status(401).json({\n                error: 'Invalid authorization'\n            });\n        }\n        // Fetch user credits\n        const { data: creditsData, error: creditsError } = await supabase.from('user_credits').select('*').eq('user_id', user.id).single();\n        // Fetch usage stats\n        const { data: usageData, error: usageError } = await supabase.from('usage_stats').select('*').eq('user_id', user.id).single();\n        res.status(200).json({\n            userCredits: creditsData || null,\n            usage: usageData || null,\n            creditsError: creditsError?.code === 'PGRST116' ? null : creditsError,\n            usageError: usageError?.code === 'PGRST116' ? null : usageError\n        });\n    } catch (error) {\n        console.error('Error fetching credit data:', error);\n        res.status(500).json({\n            error: 'Internal server error'\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/user/credits.js\n");

/***/ }),

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fuser%2Fcredits&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fuser%2Fcredits.js&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();