"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/user/consume-credits";
exports.ids = ["pages/api/user/consume-credits"];
exports.modules = {

/***/ "(api-node)/./lib/supabase-server.js":
/*!********************************!*\
  !*** ./lib/supabase-server.js ***!
  \********************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServerSupabaseClient: () => (/* binding */ createServerSupabaseClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n\nfunction createServerSupabaseClient() {\n    const supabaseUrl = \"https://vsaxiialrhpqdlcpmetn.supabase.co\";\n    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    if (!supabaseUrl || !supabaseServiceKey) {\n        throw new Error('Missing Supabase environment variables');\n    }\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaS1ub2RlKS8uL2xpYi9zdXBhYmFzZS1zZXJ2ZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBcUQ7QUFFOUMsU0FBU0M7SUFDZCxNQUFNQyxjQUFjQywwQ0FBb0M7SUFDeEQsTUFBTUcscUJBQXFCSCxRQUFRQyxHQUFHLENBQUNHLHlCQUF5QjtJQUVoRSxJQUFJLENBQUNMLGVBQWUsQ0FBQ0ksb0JBQW9CO1FBQ3ZDLE1BQU0sSUFBSUUsTUFBTTtJQUNsQjtJQUVBLE9BQU9SLG1FQUFZQSxDQUFDRSxhQUFhSSxvQkFBb0I7UUFDbkRHLE1BQU07WUFDSkMsa0JBQWtCO1lBQ2xCQyxnQkFBZ0I7UUFDbEI7SUFDRjtBQUNGIiwic291cmNlcyI6WyIvVXNlcnMvZGluZXNocy9Eb2N1bWVudHMvRGV2L1Byb2plY3RzL1lUU3VidGl0bGVFeHRyYWN0b3IvWVRTdWJ0aXRsZUV4dHJhY3Rvci9saWIvc3VwYWJhc2Utc2VydmVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUNsaWVudCB9IGZyb20gJ0BzdXBhYmFzZS9zdXBhYmFzZS1qcyc7XG5cbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVTZXJ2ZXJTdXBhYmFzZUNsaWVudCgpIHtcbiAgY29uc3Qgc3VwYWJhc2VVcmwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkw7XG4gIGNvbnN0IHN1cGFiYXNlU2VydmljZUtleSA9IHByb2Nlc3MuZW52LlNVUEFCQVNFX1NFUlZJQ0VfUk9MRV9LRVk7XG5cbiAgaWYgKCFzdXBhYmFzZVVybCB8fCAhc3VwYWJhc2VTZXJ2aWNlS2V5KSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdNaXNzaW5nIFN1cGFiYXNlIGVudmlyb25tZW50IHZhcmlhYmxlcycpO1xuICB9XG5cbiAgcmV0dXJuIGNyZWF0ZUNsaWVudChzdXBhYmFzZVVybCwgc3VwYWJhc2VTZXJ2aWNlS2V5LCB7XG4gICAgYXV0aDoge1xuICAgICAgYXV0b1JlZnJlc2hUb2tlbjogZmFsc2UsXG4gICAgICBwZXJzaXN0U2Vzc2lvbjogZmFsc2VcbiAgICB9XG4gIH0pO1xufVxuIl0sIm5hbWVzIjpbImNyZWF0ZUNsaWVudCIsImNyZWF0ZVNlcnZlclN1cGFiYXNlQ2xpZW50Iiwic3VwYWJhc2VVcmwiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIiwic3VwYWJhc2VTZXJ2aWNlS2V5IiwiU1VQQUJBU0VfU0VSVklDRV9ST0xFX0tFWSIsIkVycm9yIiwiYXV0aCIsImF1dG9SZWZyZXNoVG9rZW4iLCJwZXJzaXN0U2Vzc2lvbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api-node)/./lib/supabase-server.js\n");

/***/ }),

/***/ "(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fuser%2Fconsume-credits&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fuser%2Fconsume-credits.js&middlewareConfigBase64=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fuser%2Fconsume-credits&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fuser%2Fconsume-credits.js&middlewareConfigBase64=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/./node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_user_consume_credits_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages/api/user/consume-credits.js */ \"(api-node)/./pages/api/user/consume-credits.js\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_user_consume_credits_js__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_user_consume_credits_js__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/user/consume-credits\",\n        pathname: \"/api/user/consume-credits\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_user_consume_credits_js__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fuser%2Fconsume-credits&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fuser%2Fconsume-credits.js&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/./pages/api/user/consume-credits.js":
/*!*******************************************!*\
  !*** ./pages/api/user/consume-credits.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../lib/supabase-server */ \"(api-node)/./lib/supabase-server.js\");\n\nasync function handler(req, res) {\n    // Set CORS headers\n    res.setHeader('Access-Control-Allow-Origin', '*');\n    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');\n    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');\n    if (req.method === 'OPTIONS') {\n        return res.status(200).end();\n    }\n    if (req.method !== 'POST') {\n        return res.status(405).json({\n            error: 'Method not allowed'\n        });\n    }\n    try {\n        const { credits = 1 } = req.body;\n        // Get user from auth header\n        const authHeader = req.headers.authorization;\n        if (!authHeader) {\n            return res.status(401).json({\n                error: 'Authorization required'\n            });\n        }\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n        const { data: { user }, error: authError } = await supabase.auth.getUser(authHeader.replace('Bearer ', ''));\n        if (authError || !user) {\n            return res.status(401).json({\n                error: 'Invalid authorization'\n            });\n        }\n        // Check if user has enough credits first\n        const { data: userCredits, error: creditsError } = await supabase.from('user_credits').select('available_credits').eq('user_id', user.id).single();\n        if (creditsError || !userCredits || userCredits.available_credits < credits) {\n            return res.status(403).json({\n                error: `Insufficient credits. You need ${credits} credit(s).`,\n                availableCredits: userCredits?.available_credits || 0\n            });\n        }\n        // Consume credits using the database function\n        const { data, error: consumeError } = await supabase.rpc('consume_credits', {\n            p_user_id: user.id,\n            p_credits: credits\n        });\n        if (consumeError) {\n            console.error('Error consuming credits:', consumeError);\n            return res.status(500).json({\n                error: 'Failed to consume credits'\n            });\n        }\n        if (!data) {\n            return res.status(403).json({\n                error: 'Insufficient credits'\n            });\n        }\n        res.status(200).json({\n            success: true,\n            creditsConsumed: credits,\n            message: `${credits} credit(s) consumed successfully`\n        });\n    } catch (error) {\n        console.error('Error consuming credits:', error);\n        res.status(500).json({\n            error: 'Internal server error'\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/user/consume-credits.js\n");

/***/ }),

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fuser%2Fconsume-credits&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fuser%2Fconsume-credits.js&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();