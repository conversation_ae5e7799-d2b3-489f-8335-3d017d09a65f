"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/subtitles/download/[...params]";
exports.ids = ["pages/api/subtitles/download/[...params]"];
exports.modules = {

/***/ "(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsubtitles%2Fdownload%2F%5B...params%5D&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fsubtitles%2Fdownload%2F%5B...params%5D.js&middlewareConfigBase64=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsubtitles%2Fdownload%2F%5B...params%5D&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fsubtitles%2Fdownload%2F%5B...params%5D.js&middlewareConfigBase64=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/./node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_subtitles_download_params_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages/api/subtitles/download/[...params].js */ \"(api-node)/./pages/api/subtitles/download/[...params].js\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_subtitles_download_params_js__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_subtitles_download_params_js__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/subtitles/download/[...params]\",\n        pathname: \"/api/subtitles/download/[...params]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_subtitles_download_params_js__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsubtitles%2Fdownload%2F%5B...params%5D&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fsubtitles%2Fdownload%2F%5B...params%5D.js&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/./pages/api/subtitles/download/[...params].js":
/*!*****************************************************!*\
  !*** ./pages/api/subtitles/download/[...params].js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var youtube_dl_exec__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! youtube-dl-exec */ \"youtube-dl-exec\");\n\n// Helper function to download and parse subtitle content\nconst downloadAndParseSubtitles = async (subtitleUrl)=>{\n    try {\n        const response = await fetch(subtitleUrl);\n        const content = await response.text();\n        // Parse VTT format\n        if (content.includes('WEBVTT')) {\n            return parseVTTContent(content);\n        }\n        // Try to parse as JSON3 format (YouTube's format)\n        try {\n            const jsonData = JSON.parse(content);\n            if (jsonData.events) {\n                return parseJSON3Content(jsonData);\n            }\n        } catch (e) {\n        // Not JSON, continue with VTT parsing\n        }\n        // Fallback to VTT parsing\n        return parseVTTContent(content);\n    } catch (error) {\n        console.error('Error downloading/parsing subtitles:', error);\n        return [];\n    }\n};\n// Parse VTT subtitle format\nconst parseVTTContent = (content)=>{\n    const subtitles = [];\n    const lines = content.split('\\n');\n    for(let i = 0; i < lines.length; i++){\n        const line = lines[i].trim();\n        // Look for timestamp lines (format: 00:00:00.000 --> 00:00:03.000)\n        if (line.includes('-->')) {\n            // Extract timestamp part (before any alignment/position attributes)\n            const timestampMatch = line.match(/^([\\d:.,]+)\\s*-->\\s*([\\d:.,]+)/);\n            if (!timestampMatch) continue;\n            const startTime = timestampMatch[1];\n            const endTime = timestampMatch[2];\n            const start = parseVTTTime(startTime);\n            const end = parseVTTTime(endTime);\n            // Get the text lines that follow\n            const textLines = [];\n            const wordTimingLines = [];\n            i++; // Move to next line after timestamp\n            while(i < lines.length && !lines[i].includes('-->')){\n                const textLine = lines[i].trim();\n                // Break on empty line (indicates end of this subtitle block)\n                if (textLine === '') {\n                    break;\n                }\n                if (textLine) {\n                    // Check if this line contains word-level timing tags\n                    const hasWordTiming = textLine.includes('<c>') || textLine.match(/<\\d+:\\d+:\\d+\\.\\d+>/);\n                    if (hasWordTiming) {\n                        // Store word-timing lines for potential extraction\n                        wordTimingLines.push(textLine);\n                    } else {\n                        // This is a clean text line, process it\n                        let cleanText = textLine// Remove any remaining VTT formatting tags\n                        .replace(/<[^>]*>/g, '')// Clean up extra spaces\n                        .replace(/\\s+/g, ' ').trim();\n                        if (cleanText && cleanText.length > 0) {\n                            textLines.push(cleanText);\n                        }\n                    }\n                }\n                i++;\n            }\n            i--; // Step back one since the loop will increment\n            // If no clean text lines found, extract text from word-timing lines\n            if (textLines.length === 0 && wordTimingLines.length > 0) {\n                wordTimingLines.forEach((line)=>{\n                    let cleanText = line// Remove word-level timing tags like <00:00:01.920>\n                    .replace(/<\\d+:\\d+:\\d+\\.\\d+>/g, '')// Remove <c> tags and other formatting\n                    .replace(/<[^>]*>/g, '')// Clean up extra spaces\n                    .replace(/\\s+/g, ' ').trim();\n                    if (cleanText && cleanText.length > 0) {\n                        textLines.push(cleanText);\n                    }\n                });\n            }\n            // Only add subtitle if we have meaningful text and reasonable duration\n            const duration = end - start;\n            const finalText = textLines.join(' ').trim();\n            if (finalText && finalText.length > 0 && duration > 0.1 && duration < 30) {\n                subtitles.push({\n                    start,\n                    end,\n                    text: finalText\n                });\n            }\n        }\n    }\n    // Merge consecutive subtitles with very short gaps (less than 0.2 seconds) and similar text\n    const mergedSubtitles = [];\n    for(let i = 0; i < subtitles.length; i++){\n        const current = subtitles[i];\n        const next = subtitles[i + 1];\n        // Only merge if gap is very small and texts are different (avoid duplicates)\n        if (next && next.start - current.end < 0.2 && current.text && next.text && current.text !== next.text && !next.text.includes(current.text)) {\n            // Merge with next subtitle\n            mergedSubtitles.push({\n                start: current.start,\n                end: next.end,\n                text: `${current.text} ${next.text}`.trim()\n            });\n            i++; // Skip the next subtitle since we merged it\n        } else {\n            mergedSubtitles.push(current);\n        }\n    }\n    return mergedSubtitles;\n};\n// Parse JSON3 subtitle format (YouTube's format)\nconst parseJSON3Content = (jsonData)=>{\n    const subtitles = [];\n    if (jsonData.events) {\n        for (const event of jsonData.events){\n            if (event.segs) {\n                let text = '';\n                for (const seg of event.segs){\n                    if (seg.utf8) {\n                        text += seg.utf8;\n                    }\n                }\n                if (text.trim()) {\n                    subtitles.push({\n                        start: event.tStartMs / 1000,\n                        end: (event.tStartMs + event.dDurationMs) / 1000,\n                        text: text.trim()\n                    });\n                }\n            }\n        }\n    }\n    return subtitles;\n};\n// Parse VTT time format to seconds\nconst parseVTTTime = (timeStr)=>{\n    // Handle both comma and dot as decimal separator\n    const normalizedTime = timeStr.replace(',', '.');\n    const parts = normalizedTime.split(':');\n    if (parts.length === 3) {\n        const hours = parseInt(parts[0]) || 0;\n        const minutes = parseInt(parts[1]) || 0;\n        const seconds = parseFloat(parts[2]) || 0;\n        return hours * 3600 + minutes * 60 + seconds;\n    } else if (parts.length === 2) {\n        // Handle MM:SS.mmm format\n        const minutes = parseInt(parts[0]) || 0;\n        const seconds = parseFloat(parts[1]) || 0;\n        return minutes * 60 + seconds;\n    }\n    return 0;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (req, res)=>{\n    // Set CORS headers\n    res.setHeader('Access-Control-Allow-Origin', '*');\n    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');\n    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');\n    if (req.method === 'OPTIONS') {\n        res.status(200).end();\n        return;\n    }\n    if (req.method !== 'GET') {\n        res.status(405).json({\n            error: 'Method not allowed'\n        });\n        return;\n    }\n    try {\n        // Consume credits first\n        const authHeader = req.headers.authorization;\n        if (authHeader) {\n            try {\n                const consumeResponse = await fetch(`${req.headers.host?.includes('localhost') ? 'http' : 'https'}://${req.headers.host}/api/user/consume-credits`, {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'Authorization': authHeader\n                    },\n                    body: JSON.stringify({\n                        credits: 1\n                    })\n                });\n                if (!consumeResponse.ok) {\n                    const errorData = await consumeResponse.json();\n                    return res.status(consumeResponse.status).json(errorData);\n                }\n            } catch (creditError) {\n                console.error('Error consuming credits:', creditError);\n                return res.status(500).json({\n                    error: 'Failed to process credit consumption'\n                });\n            }\n        }\n        const { params } = req.query;\n        // Log the incoming request details for debugging\n        console.log('Request URL:', req.url);\n        console.log('Query params:', req.query);\n        console.log('Params:', params);\n        // Handle the case where params might be a string instead of an array\n        const paramArray = params?.[0]?.split('-') || [];\n        if (paramArray.length < 2) {\n            return res.status(400).json({\n                error: 'Invalid parameters. Expected format: videoId-langCode'\n            });\n        }\n        // Extract videoId and langCode from the params\n        const [videoId, langCode] = paramArray;\n        if (!videoId) {\n            return res.status(400).json({\n                error: 'Video ID is required'\n            });\n        }\n        if (!langCode) {\n            return res.status(400).json({\n                error: 'Language code is required'\n            });\n        }\n        console.log(`Processing request for video: ${videoId}, language: ${langCode}`);\n        // Get video info and extract subtitles using youtube-dl-exec\n        const videoUrl = `https://www.youtube.com/watch?v=${videoId}`;\n        // Configure youtube-dl-exec options\n        const options = {\n            dumpSingleJson: true,\n            noCheckCertificates: true,\n            noWarnings: true,\n            preferFreeFormats: true,\n            addHeader: [\n                'referer:youtube.com',\n                'user-agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'\n            ]\n        };\n        // Add proxy if configured\n        if (process.env.SOCKET_URL && process.env.SOCKET_ENABLED === 'true') {\n            options.proxy = process.env.SOCKET_URL;\n        }\n        const videoInfo = await youtube_dl_exec__WEBPACK_IMPORTED_MODULE_0__(videoUrl, options);\n        let subtitles = [];\n        let subtitleUrl = null;\n        // Find the subtitle track for the requested language\n        if (videoInfo.subtitles && videoInfo.subtitles[langCode]) {\n            const subtitleTracks = videoInfo.subtitles[langCode];\n            // Prefer VTT format, fallback to other formats\n            const track = subtitleTracks.find((t)=>t.ext === 'vtt') || subtitleTracks[0];\n            if (track) {\n                subtitleUrl = track.url;\n                console.log(`Found subtitles for ${langCode}: ${track.ext} format`);\n            } else {\n                return res.status(404).json({\n                    error: 'Subtitles not found for the specified language'\n                });\n            }\n        } else if (videoInfo.automatic_captions && videoInfo.automatic_captions[langCode]) {\n            // Try automatic captions if manual subtitles not available\n            const autoCaptions = videoInfo.automatic_captions[langCode];\n            const track = autoCaptions.find((t)=>t.ext === 'vtt') || autoCaptions[0];\n            if (track) {\n                subtitleUrl = track.url;\n                console.log(`Found auto-generated subtitles for ${langCode}: ${track.ext} format`);\n            } else {\n                return res.status(404).json({\n                    error: 'Subtitles not found for the specified language'\n                });\n            }\n        } else {\n            return res.status(404).json({\n                error: 'No subtitles available for this video'\n            });\n        }\n        // Download and parse subtitles\n        if (subtitleUrl) {\n            console.log(`Downloading subtitles from ${subtitleUrl}`);\n            subtitles = await downloadAndParseSubtitles(subtitleUrl);\n        }\n        res.status(200).json({\n            videoId,\n            title: videoInfo.title || 'YouTube Video',\n            channel: videoInfo.uploader || videoInfo.channel || 'Unknown Channel',\n            channelId: videoInfo.uploader_id || videoInfo.channel_id || null,\n            channelUrl: videoInfo.uploader_url || videoInfo.channel_url || null,\n            thumbnail: videoInfo.thumbnail || `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,\n            duration: videoInfo.duration || null,\n            uploadDate: videoInfo.upload_date || null,\n            viewCount: videoInfo.view_count || null,\n            language: langCode,\n            subtitles,\n            formats: {\n                vtt: generateVTT(subtitles, videoInfo, langCode),\n                srt: generateSRT(subtitles),\n                txt: generateTXT(subtitles, videoInfo, langCode),\n                json: generateJSON(subtitles, videoInfo, langCode)\n            }\n        });\n    } catch (error) {\n        console.error('Error downloading subtitles:', error);\n        res.status(500).json({\n            error: 'Failed to download subtitles',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        });\n    }\n});\n// Generate VTT format\nconst generateVTT = (subtitles, videoInfo, langCode)=>{\n    let vtt = 'WEBVTT\\n';\n    vtt += `Kind: captions\\n`;\n    vtt += `Language: ${langCode}\\n\\n`;\n    if (videoInfo.title) {\n        vtt += `NOTE\\n`;\n        vtt += `Title: ${videoInfo.title}\\n`;\n        if (videoInfo.uploader || videoInfo.channel) {\n            vtt += `Channel: ${videoInfo.uploader || videoInfo.channel}\\n`;\n        }\n        vtt += `Video ID: ${videoInfo.id || 'Unknown'}\\n\\n`;\n    }\n    subtitles.forEach((subtitle)=>{\n        const startTime = formatVTTTime(subtitle.start);\n        const endTime = formatVTTTime(subtitle.end);\n        vtt += `${startTime} --> ${endTime}\\n`;\n        vtt += `${subtitle.text}\\n\\n`;\n    });\n    return vtt;\n};\n// Generate SRT format\nconst generateSRT = (subtitles)=>{\n    let srt = '';\n    subtitles.forEach((subtitle, index)=>{\n        const startTime = formatSRTTime(subtitle.start);\n        const endTime = formatSRTTime(subtitle.end);\n        srt += `${index + 1}\\n`;\n        srt += `${startTime} --> ${endTime}\\n`;\n        srt += `${subtitle.text}\\n\\n`;\n    });\n    return srt;\n};\n// Generate TXT format with metadata\nconst generateTXT = (subtitles, videoInfo, langCode)=>{\n    let txt = '';\n    // Add metadata header\n    if (videoInfo.title) {\n        txt += `Title: ${videoInfo.title}\\n`;\n    }\n    if (videoInfo.uploader || videoInfo.channel) {\n        txt += `Channel: ${videoInfo.uploader || videoInfo.channel}\\n`;\n    }\n    txt += `Video ID: ${videoInfo.id || 'Unknown'}\\n`;\n    txt += `Language: ${langCode}\\n`;\n    if (videoInfo.duration) {\n        txt += `Duration: ${Math.floor(videoInfo.duration / 60)}:${String(videoInfo.duration % 60).padStart(2, '0')}\\n`;\n    }\n    txt += `\\n--- Subtitles ---\\n\\n`;\n    subtitles.forEach((subtitle)=>{\n        const startMin = Math.floor(subtitle.start / 60);\n        const startSec = Math.floor(subtitle.start % 60);\n        const endMin = Math.floor(subtitle.end / 60);\n        const endSec = Math.floor(subtitle.end % 60);\n        txt += `[${startMin}:${String(startSec).padStart(2, '0')} - ${endMin}:${String(endSec).padStart(2, '0')}] ${subtitle.text}\\n`;\n    });\n    return txt;\n};\n// Generate JSON format\nconst generateJSON = (subtitles, videoInfo, langCode)=>{\n    return JSON.stringify({\n        metadata: {\n            title: videoInfo.title || 'YouTube Video',\n            channel: videoInfo.uploader || videoInfo.channel || 'Unknown Channel',\n            channelId: videoInfo.uploader_id || videoInfo.channel_id || null,\n            channelUrl: videoInfo.uploader_url || videoInfo.channel_url || null,\n            videoId: videoInfo.id || 'Unknown',\n            language: langCode,\n            duration: videoInfo.duration || null,\n            uploadDate: videoInfo.upload_date || null,\n            viewCount: videoInfo.view_count || null,\n            thumbnail: videoInfo.thumbnail || null,\n            extractedAt: new Date().toISOString(),\n            totalSubtitles: subtitles.length\n        },\n        subtitles: subtitles.map((subtitle, index)=>({\n                index: index + 1,\n                start: subtitle.start,\n                end: subtitle.end,\n                duration: subtitle.end - subtitle.start,\n                text: subtitle.text,\n                startTime: formatVTTTime(subtitle.start),\n                endTime: formatVTTTime(subtitle.end)\n            }))\n    }, null, 2);\n};\n// Format time for VTT (HH:MM:SS.mmm)\nconst formatVTTTime = (seconds)=>{\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor(seconds % 3600 / 60);\n    const secs = seconds % 60;\n    return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${secs.toFixed(3).padStart(6, '0')}`;\n};\n// Format time for SRT (HH:MM:SS,mmm)\nconst formatSRTTime = (seconds)=>{\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor(seconds % 3600 / 60);\n    const secs = seconds % 60;\n    return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${secs.toFixed(3).replace('.', ',').padStart(6, '0')}`;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/subtitles/download/[...params].js\n");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "youtube-dl-exec":
/*!**********************************!*\
  !*** external "youtube-dl-exec" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("youtube-dl-exec");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsubtitles%2Fdownload%2F%5B...params%5D&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fsubtitles%2Fdownload%2F%5B...params%5D.js&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();