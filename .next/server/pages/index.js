/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "(pages-dir-node)/./components/FAQ.tsx":
/*!****************************!*\
  !*** ./components/FAQ.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(pages-dir-node)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(pages-dir-node)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Download_FileText_Globe_HelpCircle_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Download,FileText,Globe,HelpCircle,Shield,Zap!=!lucide-react */ \"(pages-dir-node)/__barrel_optimize__?names=ChevronDown,ChevronUp,Download,FileText,Globe,HelpCircle,Shield,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_2__, _components_ui_card__WEBPACK_IMPORTED_MODULE_3__, _components_ui_button__WEBPACK_IMPORTED_MODULE_4__]);\n([framer_motion__WEBPACK_IMPORTED_MODULE_2__, _components_ui_card__WEBPACK_IMPORTED_MODULE_3__, _components_ui_button__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nconst FAQ = ({ showHeader = true })=>{\n    const [openItems, setOpenItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        0\n    ]); // First item open by default\n    const toggleItem = (index)=>{\n        setOpenItems((prev)=>prev.includes(index) ? prev.filter((i)=>i !== index) : [\n                ...prev,\n                index\n            ]);\n    };\n    const faqs = [\n        {\n            question: \"Can I download subtitles from any YouTube video?\",\n            answer: \"Yes, you can download subtitles from any YouTube video that has subtitles available. This includes both manually uploaded subtitles and auto-generated captions. Our YouTube subtitle downloader works with videos in all languages and formats.\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Download_FileText_Globe_HelpCircle_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Download, {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/FAQ.tsx\",\n                lineNumber: 26,\n                columnNumber: 13\n            }, undefined),\n            keywords: \"download YouTube subtitles, YouTube video subtitles, extract YouTube captions\"\n        },\n        {\n            question: \"Are auto-generated YouTube subtitles supported?\",\n            answer: \"Absolutely! Our YouTube transcript extractor can fetch auto-generated captions from any video. Auto-generated subtitles are created by YouTube's AI and are available for most videos. You can download these in SRT, VTT, or TXT format.\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Download_FileText_Globe_HelpCircle_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Zap, {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/FAQ.tsx\",\n                lineNumber: 32,\n                columnNumber: 13\n            }, undefined),\n            keywords: \"auto-generated YouTube captions, YouTube AI subtitles, automatic captions download\"\n        },\n        {\n            question: \"Is it legal to download subtitles from YouTube videos?\",\n            answer: \"For personal, educational, or non-commercial use, downloading YouTube subtitles is generally acceptable under fair use. Always credit the original creator and respect copyright laws. Commercial use may require permission from the content owner.\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Download_FileText_Globe_HelpCircle_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Shield, {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/FAQ.tsx\",\n                lineNumber: 38,\n                columnNumber: 13\n            }, undefined),\n            keywords: \"legal YouTube subtitle download, fair use YouTube captions, copyright YouTube subtitles\"\n        },\n        {\n            question: \"What subtitle formats can I download?\",\n            answer: \"Our YouTube subtitle downloader supports two popular formats: VTT (WebVTT) and TXT (plain text with metadata). VTT works great for web videos and video players, while TXT includes video metadata and timestamped content perfect for reading transcripts.\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Download_FileText_Globe_HelpCircle_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.FileText, {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/FAQ.tsx\",\n                lineNumber: 44,\n                columnNumber: 13\n            }, undefined),\n            keywords: \"VTT YouTube subtitles, YouTube transcript TXT format, WebVTT download\"\n        },\n        {\n            question: \"How accurate are YouTube auto-generated subtitles?\",\n            answer: \"YouTube's auto-generated subtitles have improved significantly and are generally 80-95% accurate for clear speech in popular languages like English. Accuracy may vary based on audio quality, accents, and technical terminology. Always review before important use.\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Download_FileText_Globe_HelpCircle_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.HelpCircle, {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/FAQ.tsx\",\n                lineNumber: 50,\n                columnNumber: 13\n            }, undefined),\n            keywords: \"YouTube auto-caption accuracy, YouTube AI subtitle quality, auto-generated captions reliability\"\n        },\n        {\n            question: \"Can I download subtitles in different languages?\",\n            answer: \"Yes! If a YouTube video has subtitles in multiple languages, you can choose and download any available language. Our tool supports 70+ languages including English, Spanish, French, German, Japanese, Korean, Arabic, Hindi, and many more.\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Download_FileText_Globe_HelpCircle_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Globe, {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/FAQ.tsx\",\n                lineNumber: 56,\n                columnNumber: 13\n            }, undefined),\n            keywords: \"multilingual YouTube subtitles, YouTube captions different languages, international YouTube transcripts\"\n        },\n        {\n            question: \"Do I need to create an account to download subtitles?\",\n            answer: \"No account required! Our YouTube subtitle extractor is completely free and doesn't require registration. Simply paste the YouTube URL, select your preferred language and format, then download instantly. We respect your privacy and don't store any personal data.\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Download_FileText_Globe_HelpCircle_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Shield, {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/FAQ.tsx\",\n                lineNumber: 62,\n                columnNumber: 13\n            }, undefined),\n            keywords: \"free YouTube subtitle download, no registration YouTube captions, anonymous subtitle extractor\"\n        },\n        {\n            question: \"Can I download subtitles from YouTube playlists?\",\n            answer: \"Currently, our tool focuses on individual YouTube videos for the best user experience. You can extract subtitles from each video in a playlist by processing them one at a time. This ensures higher quality and more reliable downloads.\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Download_FileText_Globe_HelpCircle_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Download, {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/FAQ.tsx\",\n                lineNumber: 68,\n                columnNumber: 13\n            }, undefined),\n            keywords: \"YouTube video subtitles, individual video captions, single video transcript download\"\n        },\n        {\n            question: \"How fast is the subtitle extraction process?\",\n            answer: \"Our YouTube subtitle downloader typically extracts and processes subtitles within 5-15 seconds, depending on the video length and subtitle complexity. The process includes fetching, cleaning, and formatting the subtitles for optimal readability.\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Download_FileText_Globe_HelpCircle_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Zap, {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/FAQ.tsx\",\n                lineNumber: 74,\n                columnNumber: 13\n            }, undefined),\n            keywords: \"fast YouTube subtitle download, quick caption extraction, instant YouTube transcript\"\n        },\n        {\n            question: \"What should I do if subtitles aren't available for a video?\",\n            answer: \"If a YouTube video doesn't have subtitles, our tool will notify you. The video creator may not have uploaded subtitles, or auto-generation might be disabled. Try checking if the video has captions enabled in YouTube's settings first.\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Download_FileText_Globe_HelpCircle_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.HelpCircle, {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/FAQ.tsx\",\n                lineNumber: 80,\n                columnNumber: 13\n            }, undefined),\n            keywords: \"YouTube video no subtitles, missing YouTube captions, subtitle availability check\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: showHeader ? \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\" : \"\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n            children: [\n                showHeader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl md:text-5xl font-bold text-white mb-4\",\n                            children: \"Frequently Asked Questions\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/FAQ.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-300 max-w-2xl mx-auto\",\n                            children: \"Everything you need to know about downloading YouTube subtitles and captions\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/FAQ.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/FAQ.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: faqs.map((faq, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                delay: showHeader ? 0.1 * (index + 1) : 0.05 * index\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"bg-slate-800/80 backdrop-blur-sm border-slate-700 overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: ()=>toggleItem(index),\n                                        className: \"w-full p-6 text-left hover:bg-slate-700/50 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-purple-400\",\n                                                            children: faq.icon\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/FAQ.tsx\",\n                                                            lineNumber: 119,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-white\",\n                                                            children: faq.question\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/FAQ.tsx\",\n                                                            lineNumber: 122,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/FAQ.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-purple-400\",\n                                                    children: openItems.includes(index) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Download_FileText_Globe_HelpCircle_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.ChevronUp, {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/FAQ.tsx\",\n                                                        lineNumber: 128,\n                                                        columnNumber: 25\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Download_FileText_Globe_HelpCircle_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.ChevronDown, {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/FAQ.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/FAQ.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/FAQ.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/FAQ.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n                                        children: openItems.includes(index) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                            initial: {\n                                                height: 0,\n                                                opacity: 0\n                                            },\n                                            animate: {\n                                                height: 'auto',\n                                                opacity: 1\n                                            },\n                                            exit: {\n                                                height: 0,\n                                                opacity: 0\n                                            },\n                                            transition: {\n                                                duration: 0.3\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                className: \"px-6 pb-6 pt-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"pl-9\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300 leading-relaxed\",\n                                                        children: faq.answer\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/FAQ.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/FAQ.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/FAQ.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/FAQ.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/FAQ.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/FAQ.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 15\n                            }, undefined)\n                        }, index, false, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/FAQ.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/FAQ.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, undefined),\n                showHeader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: 0.5\n                    },\n                    className: \"mt-12 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"bg-slate-800/80 backdrop-blur-sm border-slate-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"p-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-white mb-4\",\n                                    children: \"Still have questions?\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/FAQ.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 mb-6\",\n                                    children: \"Can't find the answer you're looking for? We'd love to help you get the most out of our YouTube subtitle downloader.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/FAQ.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>{\n                                        if (false) {}\n                                    },\n                                    className: \"bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700\",\n                                    children: \"Contact Support\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/FAQ.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/FAQ.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/FAQ.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/FAQ.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/FAQ.tsx\",\n            lineNumber: 87,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/FAQ.tsx\",\n        lineNumber: 86,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FAQ);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/FAQ.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/Footer.tsx":
/*!*******************************!*\
  !*** ./components/Footer.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-icons */ \"@radix-ui/react-icons\");\n/* harmony import */ var _radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var _barrel_optimize_names_FileText_Heart_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Heart,Mail,Shield!=!lucide-react */ \"(pages-dir-node)/__barrel_optimize__?names=FileText,Heart,Mail,Shield!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_2__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst Footer = ({ onTermsClick, onPrivacyClick, onDisclaimerClick })=>{\n    const currentYear = new Date().getFullYear();\n    const links = {\n        product: [\n            {\n                name: 'Features',\n                href: '#features'\n            },\n            {\n                name: 'How to Use',\n                href: '#how-to-use'\n            },\n            {\n                name: 'Use Cases',\n                href: '#use-cases'\n            },\n            {\n                name: 'FAQ',\n                href: '#faq'\n            }\n        ],\n        legal: [\n            {\n                name: 'Privacy Policy',\n                onClick: onPrivacyClick\n            },\n            {\n                name: 'Terms of Service',\n                onClick: onTermsClick\n            },\n            {\n                name: 'Disclaimer',\n                onClick: onDisclaimerClick\n            }\n        ],\n        social: [\n            {\n                name: 'GitHub',\n                href: '#',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_1__.GitHubLogoIcon, {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 42\n                }, undefined)\n            },\n            {\n                name: 'Twitter',\n                href: '#',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_1__.TwitterLogoIcon, {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 43\n                }, undefined)\n            },\n            {\n                name: 'Email',\n                href: 'mailto:<EMAIL>',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Heart_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Mail, {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 78\n                }, undefined)\n            }\n        ]\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-slate-900 border-t border-slate-800\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 py-8 sm:py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"col-span-1 sm:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl sm:text-2xl font-bold text-white mb-3 sm:mb-4\",\n                                    children: [\n                                        \"YouTube Subtitle\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent\",\n                                            children: [\n                                                \" \",\n                                                \"Extractor\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-4 sm:mb-6 max-w-md text-sm sm:text-base\",\n                                    children: \"The most advanced YouTube subtitle extraction tool. Extract, clean, and download subtitles from videos and playlists with professional-grade accuracy.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3 sm:space-x-4\",\n                                    children: links.social.map((social)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.a, {\n                                            href: social.href,\n                                            whileHover: {\n                                                scale: 1.1\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            className: \"text-gray-400 hover:text-purple-400 transition-colors duration-200\",\n                                            \"aria-label\": social.name,\n                                            children: social.icon\n                                        }, social.name, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.1\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-base sm:text-lg font-semibold text-white mb-3 sm:mb-4\",\n                                    children: \"Product\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-1 sm:space-y-2\",\n                                    children: links.product.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: link.href,\n                                                className: \"text-gray-400 hover:text-purple-400 transition-colors duration-200 text-sm sm:text-base\",\n                                                children: link.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, link.name, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.2\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-base sm:text-lg font-semibold text-white mb-3 sm:mb-4\",\n                                    children: \"Legal\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-1 sm:space-y-2\",\n                                    children: links.legal.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: link.onClick ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: link.onClick,\n                                                className: \"text-gray-400 hover:text-purple-400 transition-colors duration-200 text-sm sm:text-base\",\n                                                children: link.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                // href={link.href}\n                                                className: \"text-gray-400 hover:text-purple-400 transition-colors duration-200 text-sm sm:text-base\",\n                                                children: link.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, link.name, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    whileInView: {\n                        opacity: 1\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.4\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"flex flex-col lg:flex-row justify-between items-center pt-6 sm:pt-8 mt-6 sm:mt-8 border-t border-slate-800 space-y-4 lg:space-y-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center text-gray-400 text-xs sm:text-sm text-center lg:text-left\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"\\xa9 \",\n                                        currentYear,\n                                        \" DownloadYTSubtitles.com. Made with\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Heart_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Heart, {\n                                    className: \"w-3 h-3 sm:w-4 sm:h-4 text-red-400 mx-1\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"for the community.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4 lg:space-x-6 text-xs sm:text-sm text-gray-400\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Heart_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Shield, {\n                                            className: \"w-3 h-3 sm:w-4 sm:h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Privacy First\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Heart_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_3__.FileText, {\n                                            className: \"w-3 h-3 sm:w-4 sm:h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Professional Quality\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/Footer.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(pages-dir-node)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_Download_HelpCircle_Home_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,Download,HelpCircle,Home,MessageSquare!=!lucide-react */ \"(pages-dir-node)/__barrel_optimize__?names=CreditCard,Download,HelpCircle,Home,MessageSquare!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/contexts/AuthContext */ \"(pages-dir-node)/./components/contexts/AuthContext.tsx\");\n/* harmony import */ var _auth_LoginButton__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./auth/LoginButton */ \"(pages-dir-node)/./components/auth/LoginButton.tsx\");\n/* harmony import */ var _auth_UserMenu__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./auth/UserMenu */ \"(pages-dir-node)/./components/auth/UserMenu.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_1__, _components_ui_button__WEBPACK_IMPORTED_MODULE_2__, _components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__, _auth_LoginButton__WEBPACK_IMPORTED_MODULE_4__, _auth_UserMenu__WEBPACK_IMPORTED_MODULE_5__]);\n([framer_motion__WEBPACK_IMPORTED_MODULE_1__, _components_ui_button__WEBPACK_IMPORTED_MODULE_2__, _components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__, _auth_LoginButton__WEBPACK_IMPORTED_MODULE_4__, _auth_UserMenu__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nconst Header = ({ currentView, onNavigate, onFeedback })=>{\n    const { user } = (0,_components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.header, {\n        initial: {\n            opacity: 0,\n            y: -20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        className: \"bg-slate-900/95 backdrop-blur-sm border-b border-slate-700 sticky top-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-3 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-14 sm:h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            className: \"flex items-center space-x-2 sm:space-x-3 cursor-pointer\",\n                            onClick: ()=>onNavigate(''),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_Download_HelpCircle_Home_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Download, {\n                                        className: \"w-4 h-4 sm:w-6 sm:h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                        lineNumber: 31,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden xs:block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-lg sm:text-xl font-bold text-white\",\n                                            children: \"DownloadYTSubtitles\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                            lineNumber: 34,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-400 hidden sm:block\",\n                                            children: \"YouTube Subtitle Extractor\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                            lineNumber: 35,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"block xs:hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-sm font-bold text-white\",\n                                        children: \"DYTS\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex items-center space-x-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: currentView === 'landing' ? 'default' : 'ghost',\n                                    size: \"sm\",\n                                    onClick: ()=>onNavigate(''),\n                                    className: currentView === 'landing' ? 'bg-purple-600 hover:bg-purple-700' : 'text-gray-300 hover:text-white',\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_Download_HelpCircle_Home_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Home, {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Home\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: currentView === 'extractor' ? 'default' : 'ghost',\n                                    size: \"sm\",\n                                    onClick: ()=>onNavigate('extractor'),\n                                    className: currentView === 'extractor' ? 'bg-purple-600 hover:bg-purple-700' : 'text-gray-300 hover:text-white',\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_Download_HelpCircle_Home_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Download, {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Extract\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: currentView === 'faq' ? 'default' : 'ghost',\n                                    size: \"sm\",\n                                    onClick: ()=>onNavigate('faq'),\n                                    className: currentView === 'faq' ? 'bg-purple-600 hover:bg-purple-700' : 'text-gray-300 hover:text-white',\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_Download_HelpCircle_Home_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_6__.HelpCircle, {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"FAQ\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: currentView === 'pricing' ? 'default' : 'ghost',\n                                    size: \"sm\",\n                                    onClick: ()=>onNavigate('pricing'),\n                                    className: currentView === 'pricing' ? 'bg-purple-600 hover:bg-purple-700' : 'text-gray-300 hover:text-white',\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_Download_HelpCircle_Home_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_6__.CreditCard, {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Pricing\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 sm:space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: onFeedback,\n                                    className: \"border-purple-500 text-purple-400 hover:bg-purple-500 hover:text-white text-xs sm:text-sm px-2 sm:px-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_Download_HelpCircle_Home_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_6__.MessageSquare, {\n                                            className: \"w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden xs:inline\",\n                                            children: \"Feedback\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"xs:hidden\",\n                                            children: \"FB\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, undefined),\n                                user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_auth_UserMenu__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    onNavigate: onNavigate\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_auth_LoginButton__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"border-purple-600 text-purple-400 hover:bg-purple-600 hover:text-white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>onNavigate('extractor'),\n                                        className: \"text-gray-300 hover:text-white p-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_Download_HelpCircle_Home_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Download, {\n                                            className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden pb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center space-x-2 overflow-x-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: currentView === 'landing' ? 'default' : 'ghost',\n                                size: \"sm\",\n                                onClick: ()=>onNavigate(''),\n                                className: `text-xs px-3 py-2 ${currentView === 'landing' ? 'bg-purple-600 hover:bg-purple-700' : 'text-gray-300 hover:text-white'}`,\n                                children: \"Home\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: currentView === 'extractor' ? 'default' : 'ghost',\n                                size: \"sm\",\n                                onClick: ()=>onNavigate('extractor'),\n                                className: `text-xs px-3 py-2 ${currentView === 'extractor' ? 'bg-purple-600 hover:bg-purple-700' : 'text-gray-300 hover:text-white'}`,\n                                children: \"Extract\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: currentView === 'faq' ? 'default' : 'ghost',\n                                size: \"sm\",\n                                onClick: ()=>onNavigate('faq'),\n                                className: `text-xs px-3 py-2 ${currentView === 'faq' ? 'bg-purple-600 hover:bg-purple-700' : 'text-gray-300 hover:text-white'}`,\n                                children: \"FAQ\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: currentView === 'pricing' ? 'default' : 'ghost',\n                                size: \"sm\",\n                                onClick: ()=>onNavigate('pricing'),\n                                className: `text-xs px-3 py-2 ${currentView === 'pricing' ? 'bg-purple-600 hover:bg-purple-700' : 'text-gray-300 hover:text-white'}`,\n                                children: \"Pricing\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/Header.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/HowToUse.tsx":
/*!*********************************!*\
  !*** ./components/HowToUse.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(pages-dir-node)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_Download_Eye_Link_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,Download,Eye,Link,Search!=!lucide-react */ \"(pages-dir-node)/__barrel_optimize__?names=ArrowRight,CheckCircle,Download,Eye,Link,Search!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_1__, _components_ui_card__WEBPACK_IMPORTED_MODULE_2__]);\n([framer_motion__WEBPACK_IMPORTED_MODULE_1__, _components_ui_card__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst HowToUse = ()=>{\n    const steps = [\n        {\n            step: 1,\n            title: \"Paste YouTube URL\",\n            description: \"Copy and paste any YouTube video URL into the input field. Our system will automatically validate the URL and detect if it's a valid YouTube video.\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Download_Eye_Link_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Link, {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/HowToUse.tsx\",\n                lineNumber: 18,\n                columnNumber: 13\n            }, undefined),\n            image: \"/images/step1-url-input.png\",\n            tips: [\n                \"Works with any YouTube video URL format\",\n                \"Automatic URL validation with instant feedback\",\n                \"Supports both youtube.com and youtu.be links\"\n            ]\n        },\n        {\n            step: 2,\n            title: \"Select Language\",\n            description: \"Browse through available subtitle languages with our searchable interface. English is automatically selected when available.\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Download_Eye_Link_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Search, {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/HowToUse.tsx\",\n                lineNumber: 30,\n                columnNumber: 13\n            }, undefined),\n            image: \"/images/step2-language-select.png\",\n            tips: [\n                \"Search through 70+ supported languages\",\n                \"Auto-generated subtitles clearly marked\",\n                \"English automatically selected as default\"\n            ]\n        },\n        {\n            step: 3,\n            title: \"Extract & Process\",\n            description: \"Watch the real-time progress as we extract and clean the subtitles. Our advanced processing ensures perfect formatting.\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Download_Eye_Link_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Download, {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/HowToUse.tsx\",\n                lineNumber: 42,\n                columnNumber: 13\n            }, undefined),\n            image: \"/images/step3-extraction.png\",\n            tips: [\n                \"Real-time progress tracking\",\n                \"Advanced subtitle cleaning algorithms\",\n                \"Automatic formatting optimization\"\n            ]\n        },\n        {\n            step: 4,\n            title: \"Preview & Download\",\n            description: \"Preview your subtitles in VTT, SRT, or TXT format before downloading. See exactly what you'll get with full content preview.\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Download_Eye_Link_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Eye, {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/HowToUse.tsx\",\n                lineNumber: 54,\n                columnNumber: 13\n            }, undefined),\n            image: \"/images/step4-preview-download.png\",\n            tips: [\n                \"Preview in multiple formats\",\n                \"Full content scrollable preview\",\n                \"One-click download in preferred format\"\n            ]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"how-to-use\",\n        className: \"py-12 sm:py-20 bg-slate-800/30\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-3 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center mb-12 sm:mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-3 sm:mb-4\",\n                            children: \"How to Use\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/HowToUse.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg sm:text-xl text-gray-300 max-w-2xl mx-auto px-2\",\n                            children: \"Extract YouTube subtitles in 4 simple steps. No registration required, completely free to use.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/HowToUse.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/HowToUse.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-12 sm:space-y-16\",\n                    children: steps.map((stepData, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 40\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: index * 0.2\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: `flex flex-col ${index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'} items-center gap-8 sm:gap-12`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 space-y-4 sm:space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 sm:space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg sm:text-2xl font-bold text-white\",\n                                                        children: stepData.step\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/HowToUse.tsx\",\n                                                        lineNumber: 96,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/HowToUse.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-purple-400\",\n                                                    children: stepData.icon\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/HowToUse.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/HowToUse.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl sm:text-3xl font-bold text-white mb-3 sm:mb-4\",\n                                                    children: stepData.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/HowToUse.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-base sm:text-lg text-gray-300 leading-relaxed\",\n                                                    children: stepData.description\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/HowToUse.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/HowToUse.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 sm:space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-base sm:text-lg font-semibold text-white\",\n                                                    children: \"Key Features:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/HowToUse.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-2\",\n                                                    children: stepData.tips.map((tip, tipIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start space-x-2 sm:space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Download_Eye_Link_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__.CheckCircle, {\n                                                                    className: \"w-4 h-4 sm:w-5 sm:h-5 text-green-400 flex-shrink-0 mt-0.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/HowToUse.tsx\",\n                                                                    lineNumber: 117,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm sm:text-base text-gray-300\",\n                                                                    children: tip\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/HowToUse.tsx\",\n                                                                    lineNumber: 118,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, tipIndex, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/HowToUse.tsx\",\n                                                            lineNumber: 116,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/HowToUse.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/HowToUse.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        index < steps.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 text-purple-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs sm:text-sm font-medium\",\n                                                    children: \"Next Step\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/HowToUse.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Download_Eye_Link_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__.ArrowRight, {\n                                                    className: \"w-3 h-3 sm:w-4 sm:h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/HowToUse.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/HowToUse.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/HowToUse.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 w-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        className: \"bg-slate-800/80 backdrop-blur-sm border-slate-700 overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"p-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"aspect-video bg-gradient-to-br from-slate-700 to-slate-800 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-16 h-16 sm:w-24 sm:h-24 mx-auto mb-3 sm:mb-4 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center\",\n                                                            children: stepData.icon\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/HowToUse.tsx\",\n                                                            lineNumber: 139,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-white font-semibold mb-2 text-sm sm:text-base\",\n                                                            children: [\n                                                                \"Step \",\n                                                                stepData.step,\n                                                                \" Preview\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/HowToUse.tsx\",\n                                                            lineNumber: 142,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-xs sm:text-sm\",\n                                                            children: \"Screenshot coming soon\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/HowToUse.tsx\",\n                                                            lineNumber: 143,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/HowToUse.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/HowToUse.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/HowToUse.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/HowToUse.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/HowToUse.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, stepData.step, true, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/HowToUse.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/HowToUse.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/HowToUse.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/HowToUse.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HowToUse);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/HowToUse.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/LandingPage.tsx":
/*!************************************!*\
  !*** ./components/LandingPage.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Crown_Download_Globe_Play_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Crown,Download,Globe,Play,Shield,Star,Users,Zap!=!lucide-react */ \"(pages-dir-node)/__barrel_optimize__?names=ArrowRight,Crown,Download,Globe,Play,Shield,Star,Users,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(pages-dir-node)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(pages-dir-node)/./components/ui/card.tsx\");\n/* harmony import */ var _HowToUse__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./HowToUse */ \"(pages-dir-node)/./components/HowToUse.tsx\");\n/* harmony import */ var _FAQ__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./FAQ */ \"(pages-dir-node)/./components/FAQ.tsx\");\n/* harmony import */ var _lib_stripe__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/stripe */ \"(pages-dir-node)/./lib/stripe.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_1__, _components_ui_button__WEBPACK_IMPORTED_MODULE_2__, _components_ui_card__WEBPACK_IMPORTED_MODULE_3__, _HowToUse__WEBPACK_IMPORTED_MODULE_4__, _FAQ__WEBPACK_IMPORTED_MODULE_5__]);\n([framer_motion__WEBPACK_IMPORTED_MODULE_1__, _components_ui_button__WEBPACK_IMPORTED_MODULE_2__, _components_ui_card__WEBPACK_IMPORTED_MODULE_3__, _HowToUse__WEBPACK_IMPORTED_MODULE_4__, _FAQ__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nconst LandingPage = ({ onGetStarted, onNavigateToPricing })=>{\n    const features = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Crown_Download_Globe_Play_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Play, {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                lineNumber: 18,\n                columnNumber: 13\n            }, undefined),\n            title: \"YouTube Video Support\",\n            description: \"Download subtitles from any YouTube video with available captions or auto-generated transcripts.\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Crown_Download_Globe_Play_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Globe, {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                lineNumber: 23,\n                columnNumber: 13\n            }, undefined),\n            title: \"70+ Languages Supported\",\n            description: \"Extract YouTube captions in multiple languages including English, Spanish, French, German, and more.\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Crown_Download_Globe_Play_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Zap, {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                lineNumber: 28,\n                columnNumber: 13\n            }, undefined),\n            title: \"Auto-Generated Captions\",\n            description: \"Advanced processing of YouTube's AI-generated subtitles with automatic cleaning and formatting.\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Crown_Download_Globe_Play_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Download, {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                lineNumber: 33,\n                columnNumber: 13\n            }, undefined),\n            title: \"VTT & TXT Formats\",\n            description: \"Download YouTube subtitles in VTT format for video players and web, or TXT with metadata for reading.\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Crown_Download_Globe_Play_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Shield, {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                lineNumber: 38,\n                columnNumber: 13\n            }, undefined),\n            title: \"Privacy Protected\",\n            description: \"No data stored on our servers. YouTube subtitle extraction happens in real-time without tracking.\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Crown_Download_Globe_Play_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Users, {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                lineNumber: 43,\n                columnNumber: 13\n            }, undefined),\n            title: \"Professional Plans\",\n            description: \"Choose from flexible pricing plans designed for individuals, teams, and businesses of all sizes.\"\n        }\n    ];\n    const useCases = [\n        {\n            title: \"Content Creators\",\n            description: \"Extract subtitles for video editing, translation, or accessibility compliance.\",\n            gradient: \"from-purple-500 to-pink-500\"\n        },\n        {\n            title: \"Students & Researchers\",\n            description: \"Get transcripts from educational videos for note-taking and research.\",\n            gradient: \"from-blue-500 to-cyan-500\"\n        },\n        {\n            title: \"Language Learners\",\n            description: \"Study foreign languages with accurate subtitles and translations.\",\n            gradient: \"from-green-500 to-emerald-500\"\n        },\n        {\n            title: \"Accessibility Teams\",\n            description: \"Create accessible content with properly formatted subtitle files.\",\n            gradient: \"from-orange-500 to-red-500\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-purple-800/20 to-pink-800/20\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 pt-12 sm:pt-20 pb-12 sm:pb-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.h1, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.2\n                                    },\n                                    className: \"text-3xl sm:text-5xl md:text-7xl font-bold text-white mb-4 sm:mb-6 leading-tight\",\n                                    children: [\n                                        \"Download YouTube\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent\",\n                                            children: [\n                                                \" \",\n                                                \"Subtitles\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.p, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.4\n                                    },\n                                    className: \"text-base sm:text-xl md:text-2xl text-gray-300 mb-6 sm:mb-8 max-w-3xl mx-auto px-2\",\n                                    children: \"Professional YouTube subtitle downloader and transcript extractor. Get YT captions in VTT and TXT formats. Export YouTube transcripts from auto-generated and manual subtitles in 70+ languages.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.6\n                                    },\n                                    className: \"flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: onGetStarted,\n                                            size: \"lg\",\n                                            className: \"w-full sm:w-auto bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Crown_Download_Globe_Play_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Play, {\n                                                    className: \"w-4 h-4 sm:w-5 sm:h-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Get Started\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"outline\",\n                                            size: \"lg\",\n                                            className: \"w-full sm:w-auto border-purple-400 text-purple-400 hover:bg-purple-400 hover:text-white px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg font-semibold rounded-full transition-all duration-300\",\n                                            onClick: ()=>{\n                                                if (false) {}\n                                            },\n                                            children: \"Learn More\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"features\",\n                className: \"py-12 sm:py-20 bg-slate-800/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-3 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-center mb-12 sm:mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-3 sm:mb-4\",\n                                    children: \"Powerful Features\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg sm:text-xl text-gray-300 max-w-2xl mx-auto px-2\",\n                                    children: \"Everything you need to extract and process YouTube subtitles professionally\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-8\",\n                            children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: index * 0.1\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    whileHover: {\n                                        scale: 1.02\n                                    },\n                                    className: \"bg-slate-800/80 backdrop-blur-sm rounded-xl p-4 sm:p-6 border border-slate-700 hover:border-purple-500 transition-all duration-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-purple-400 mb-3 sm:mb-4\",\n                                            children: feature.icon\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg sm:text-xl font-semibold text-white mb-2\",\n                                            children: feature.title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm sm:text-base text-gray-300\",\n                                            children: feature.description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12 sm:py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-3 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-center mb-12 sm:mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-3 sm:mb-4\",\n                                    children: \"Perfect For Everyone\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg sm:text-xl text-gray-300 max-w-2xl mx-auto px-2\",\n                                    children: \"From content creators to researchers, our tool serves diverse needs\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-8\",\n                            children: useCases.map((useCase, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: index % 2 === 0 ? -20 : 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: index * 0.2\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"relative overflow-hidden rounded-xl bg-slate-800/80 backdrop-blur-sm border border-slate-700 p-4 sm:p-8 hover:border-purple-500 transition-all duration-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `absolute inset-0 bg-gradient-to-r ${useCase.gradient} opacity-10`\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl sm:text-2xl font-bold text-white mb-3 sm:mb-4\",\n                                                    children: useCase.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300 text-base sm:text-lg\",\n                                                    children: useCase.description\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                lineNumber: 183,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_HowToUse__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"faq\",\n                className: \"py-12 sm:py-20 bg-slate-800/30\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto px-3 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-center mb-12 sm:mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-3 sm:mb-4\",\n                                    children: \"Frequently Asked Questions\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg sm:text-xl text-gray-300 max-w-2xl mx-auto px-2\",\n                                    children: \"Everything you need to know about downloading YouTube subtitles\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FAQ__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            showHeader: false\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12 sm:py-20 bg-slate-800/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-3 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-4 sm:mb-6\",\n                                    children: \"Choose Your Plan\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg sm:text-xl text-gray-300 max-w-3xl mx-auto\",\n                                    children: \"Professional subtitle extraction with flexible pricing for every need\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6 sm:gap-8 mb-12\",\n                            children: Object.entries(_lib_stripe__WEBPACK_IMPORTED_MODULE_6__.PRICING_TIERS).map(([key, tier], index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: index * 0.1\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: `relative overflow-hidden ${tier.popular ? 'border-purple-500 shadow-lg shadow-purple-500/20 scale-105' : 'border-slate-700'} bg-slate-800/80 backdrop-blur-sm h-full`,\n                                        children: [\n                                            tier.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-0 left-0 right-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-r from-purple-500 to-pink-500 text-white text-center py-2 text-sm font-medium\",\n                                                    children: \"Most Popular\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                className: tier.popular ? 'pt-12' : 'pt-6',\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                            className: \"text-white flex items-center gap-2\",\n                                                            children: [\n                                                                key === 'starter' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Crown_Download_Globe_Play_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Zap, {\n                                                                    className: \"w-5 h-5 text-blue-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                                                    lineNumber: 289,\n                                                                    columnNumber: 47\n                                                                }, undefined),\n                                                                key === 'pro' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Crown_Download_Globe_Play_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Star, {\n                                                                    className: \"w-5 h-5 text-purple-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                                                    lineNumber: 290,\n                                                                    columnNumber: 43\n                                                                }, undefined),\n                                                                key === 'premium' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Crown_Download_Globe_Play_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Crown, {\n                                                                    className: \"w-5 h-5 text-yellow-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                                                    lineNumber: 291,\n                                                                    columnNumber: 47\n                                                                }, undefined),\n                                                                tier.name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                        className: \"text-gray-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-baseline gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-3xl font-bold text-white\",\n                                                                    children: [\n                                                                        \"$\",\n                                                                        tier.price\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                                                    lineNumber: 297,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"/month\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                                                    lineNumber: 298,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                className: \"space-y-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-2\",\n                                                    children: tier.features.slice(0, 4).map((feature, featureIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start gap-2 text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                                                    lineNumber: 307,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-300\",\n                                                                    children: feature\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                                                    lineNumber: 308,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, featureIndex, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 25\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, key, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.4\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: onNavigateToPricing,\n                                variant: \"outline\",\n                                size: \"lg\",\n                                className: \"border-purple-500 text-purple-400 hover:bg-purple-500 hover:text-white\",\n                                children: [\n                                    \"View All Plans & Features\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Crown_Download_Globe_Play_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__.ArrowRight, {\n                                        className: \"w-4 h-4 ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                lineNumber: 250,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12 sm:py-20 bg-gradient-to-r from-purple-800/20 to-pink-800/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto px-3 sm:px-6 lg:px-8 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-4 sm:mb-6\",\n                                children: \"Ready to Extract Subtitles?\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg sm:text-xl text-gray-300 mb-6 sm:mb-8 px-2\",\n                                children: \"Start extracting professional-quality subtitles in seconds\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: onGetStarted,\n                                size: \"lg\",\n                                className: \"w-full sm:w-auto bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 sm:px-12 py-3 sm:py-4 text-lg sm:text-xl font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Crown_Download_Globe_Play_Shield_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Download, {\n                                        className: \"w-5 h-5 sm:w-6 sm:h-6 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Start Extracting Now\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                    lineNumber: 340,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n                lineNumber: 339,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/LandingPage.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LandingPage);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/LandingPage.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/auth/LoginButton.tsx":
/*!*****************************************!*\
  !*** ./components/auth/LoginButton.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(pages-dir-node)/./components/ui/button.tsx\");\n/* harmony import */ var _components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/contexts/AuthContext */ \"(pages-dir-node)/./components/contexts/AuthContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,LogIn!=!lucide-react */ \"(pages-dir-node)/__barrel_optimize__?names=Loader2,LogIn!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_button__WEBPACK_IMPORTED_MODULE_2__, _components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__]);\n([_components_ui_button__WEBPACK_IMPORTED_MODULE_2__, _components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst LoginButton = ({ variant = 'default', size = 'default', className = '', children })=>{\n    const { signInWithGoogle, loading } = (0,_components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n        onClick: signInWithGoogle,\n        disabled: loading,\n        variant: variant,\n        size: size,\n        className: className,\n        children: [\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Loader2, {\n                className: \"w-4 h-4 mr-2 animate-spin\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/LoginButton.tsx\",\n                lineNumber: 30,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_4__.LogIn, {\n                className: \"w-4 h-4 mr-2\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/LoginButton.tsx\",\n                lineNumber: 32,\n                columnNumber: 9\n            }, undefined),\n            children || 'Sign in with Google'\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/LoginButton.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LoginButton);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/auth/LoginButton.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/auth/UserMenu.tsx":
/*!**************************************!*\
  !*** ./components/auth/UserMenu.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(pages-dir-node)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(pages-dir-node)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/avatar */ \"(pages-dir-node)/./components/ui/avatar.tsx\");\n/* harmony import */ var _components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/contexts/AuthContext */ \"(pages-dir-node)/./components/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/hooks/useSubscription */ \"(pages-dir-node)/./components/hooks/useSubscription.ts\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_Crown_LogOut_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,Crown,LogOut,Settings,User!=!lucide-react */ \"(pages-dir-node)/__barrel_optimize__?names=CreditCard,Crown,LogOut,Settings,User!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_button__WEBPACK_IMPORTED_MODULE_2__, _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__, _components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__, _components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__, _components_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_6__]);\n([_components_ui_button__WEBPACK_IMPORTED_MODULE_2__, _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__, _components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__, _components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__, _components_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nconst UserMenu = ({ onNavigate })=>{\n    const { user, signOut } = (0,_components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const { subscription } = (0,_components_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_6__.useSubscription)();\n    if (!user) return null;\n    const getInitials = (name)=>{\n        return name.split(' ').map((word)=>word[0]).join('').toUpperCase().slice(0, 2);\n    };\n    const displayName = user.user_metadata?.full_name || user.user_metadata?.name || user.email?.split('@')[0] || 'User';\n    const avatarUrl = user.user_metadata?.avatar_url || user.user_metadata?.picture;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenu, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuTrigger, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    variant: \"ghost\",\n                    className: \"relative h-8 w-8 rounded-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                        className: \"h-8 w-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarImage, {\n                                src: avatarUrl,\n                                alt: displayName\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarFallback, {\n                                className: \"bg-purple-600 text-white\",\n                                children: getInitials(displayName)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuContent, {\n                className: \"w-56\",\n                align: \"end\",\n                forceMount: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuLabel, {\n                        className: \"font-normal\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium leading-none\",\n                                    children: displayName\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs leading-none text-muted-foreground\",\n                                    children: user.email\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, undefined),\n                                subscription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1 mt-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_Crown_LogOut_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Crown, {\n                                            className: \"w-3 h-3 text-yellow-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs font-medium text-yellow-600 capitalize\",\n                                            children: [\n                                                subscription.tier,\n                                                \" Plan\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuSeparator, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                        onClick: ()=>onNavigate('dashboard'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_Crown_LogOut_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__.User, {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                        onClick: ()=>onNavigate('pricing'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_Crown_LogOut_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__.CreditCard, {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Subscription\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                        onClick: ()=>onNavigate('settings'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_Crown_LogOut_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Settings, {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Settings\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuSeparator, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                        onClick: signOut,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_Crown_LogOut_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__.LogOut, {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Sign out\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (UserMenu);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/auth/UserMenu.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/contexts/AuthContext.tsx":
/*!*********************************************!*\
  !*** ./components/contexts/AuthContext.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(pages-dir-node)/./lib/supabase.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_hot_toast__WEBPACK_IMPORTED_MODULE_3__]);\nreact_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Get initial session\n            const getInitialSession = {\n                \"AuthProvider.useEffect.getInitialSession\": async ()=>{\n                    try {\n                        const { data: { session }, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getSession();\n                        if (error) {\n                            console.error('Error getting session:', error);\n                            setError(error.message);\n                        } else {\n                            setUser(session?.user || null);\n                        }\n                    } catch (err) {\n                        console.error('Error in getInitialSession:', err);\n                        setError('Failed to get session');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.getInitialSession\"];\n            getInitialSession();\n            // Listen for auth changes\n            const { data: { subscription } } = _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.onAuthStateChange({\n                \"AuthProvider.useEffect\": async (event, session)=>{\n                    console.log('Auth state changed:', event, session?.user?.email);\n                    if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {\n                        setUser(session?.user || null);\n                        setError(null);\n                        // Create or update user profile\n                        if (session?.user) {\n                            await createOrUpdateUserProfile(session.user);\n                        }\n                    } else if (event === 'SIGNED_OUT') {\n                        setUser(null);\n                        setError(null);\n                    }\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>subscription.unsubscribe()\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const createOrUpdateUserProfile = async (user)=>{\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from('users').upsert({\n                id: user.id,\n                email: user.email || '',\n                full_name: user.user_metadata?.full_name || user.user_metadata?.name || null,\n                avatar_url: user.user_metadata?.avatar_url || user.user_metadata?.picture || null,\n                updated_at: new Date().toISOString()\n            }, {\n                onConflict: 'id'\n            });\n            if (error) {\n                console.error('Error creating/updating user profile:', error);\n            }\n        } catch (err) {\n            console.error('Error in createOrUpdateUserProfile:', err);\n        }\n    };\n    const signInWithGoogle = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithOAuth({\n                provider: 'google',\n                options: {\n                    redirectTo: `${window.location.origin}/auth/callback`\n                }\n            });\n            if (error) {\n                setError(error.message);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error('Failed to sign in with Google');\n            }\n        } catch (err) {\n            console.error('Error signing in with Google:', err);\n            setError('Failed to sign in');\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error('Failed to sign in with Google');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const signOut = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signOut();\n            if (error) {\n                setError(error.message);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error('Failed to sign out');\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success('Signed out successfully');\n            }\n        } catch (err) {\n            console.error('Error signing out:', err);\n            setError('Failed to sign out');\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error('Failed to sign out');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const refreshUser = async ()=>{\n        try {\n            const { data: { user }, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getUser();\n            if (error) {\n                setError(error.message);\n            } else {\n                setUser(user);\n            }\n        } catch (err) {\n            console.error('Error refreshing user:', err);\n            setError('Failed to refresh user');\n        }\n    };\n    const value = {\n        user,\n        loading,\n        error,\n        signInWithGoogle,\n        signOut,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/contexts/AuthContext.tsx\",\n        lineNumber: 164,\n        columnNumber: 5\n    }, undefined);\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/hooks/useSubscription.ts":
/*!*********************************************!*\
  !*** ./components/hooks/useSubscription.ts ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSubscription: () => (/* binding */ useSubscription)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase */ \"(pages-dir-node)/./lib/supabase.ts\");\n/* harmony import */ var _lib_stripe__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/stripe */ \"(pages-dir-node)/./lib/stripe.ts\");\n/* harmony import */ var _components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/contexts/AuthContext */ \"(pages-dir-node)/./components/contexts/AuthContext.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__, react_hot_toast__WEBPACK_IMPORTED_MODULE_4__]);\n([_components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__, react_hot_toast__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst useSubscription = ()=>{\n    const { user } = (0,_components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [userCredits, setUserCredits] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [usage, setUsage] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSubscription.useEffect\": ()=>{\n            if (user) {\n                fetchCreditData();\n            } else {\n                setUserCredits(null);\n                setUsage(null);\n                setLoading(false);\n            }\n        }\n    }[\"useSubscription.useEffect\"], [\n        user\n    ]);\n    const fetchCreditData = async ()=>{\n        if (!user) return;\n        try {\n            setLoading(true);\n            setError(null);\n            // Get auth token for API request\n            const { data: { session } } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.getSession();\n            const authHeaders = session?.access_token ? {\n                'Authorization': `Bearer ${session.access_token}`\n            } : {};\n            const response = await fetch('/api/user/credits', {\n                headers: {\n                    'Content-Type': 'application/json',\n                    ...authHeaders\n                }\n            });\n            if (!response.ok) {\n                throw new Error('Failed to fetch credit data');\n            }\n            const data = await response.json();\n            setUserCredits(data.userCredits);\n            setUsage(data.usage);\n        } catch (err) {\n            console.error('Error fetching credit data:', err);\n            setError('Failed to fetch credit data');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const canPerformAction = (creditsNeeded = 1)=>{\n        if (!userCredits) {\n            return false;\n        }\n        // Check if user has enough available credits\n        return userCredits.available_credits >= creditsNeeded;\n    };\n    // Backward compatibility - check if user can extract a complete video (1 credit now)\n    const canExtractVideo = ()=>{\n        return canPerformAction(1); // Direct download = 1 credit\n    };\n    const getRemainingCredits = ()=>{\n        if (!userCredits) {\n            return 0;\n        }\n        return userCredits.available_credits;\n    };\n    // Backward compatibility - get remaining complete extractions\n    const getRemainingExtractions = ()=>{\n        const remainingCredits = getRemainingCredits();\n        return remainingCredits; // Each extraction needs 1 credit now\n    };\n    // New: Get simple usage information\n    const getUsageInfo = ()=>{\n        if (!userCredits) {\n            return null;\n        }\n        return {\n            creditsPerAction: 1,\n            actionsPerExtraction: 1 // 1 action per extraction\n        };\n    };\n    // New: Check if user should be warned about credit usage\n    const shouldWarnAboutCredits = (creditsNeeded = 1)=>{\n        const remainingCredits = getRemainingCredits();\n        const warningThreshold = 10; // Warn when less than 10 credits remaining\n        return remainingCredits <= warningThreshold || remainingCredits < creditsNeeded;\n    };\n    // New: Get smart usage suggestions\n    const getUsageSuggestions = ()=>{\n        const remainingCredits = getRemainingCredits();\n        const suggestions = [];\n        if (remainingCredits <= 5) {\n            suggestions.push({\n                type: 'warning',\n                message: 'Low credits remaining. Consider purchasing more credits.',\n                action: 'upgrade'\n            });\n        }\n        if (remainingCredits <= 2) {\n            suggestions.push({\n                type: 'error',\n                message: 'Very low credits. Use English default to save credits.',\n                action: 'use-english-default'\n            });\n        }\n        if (remainingCredits === 0) {\n            suggestions.push({\n                type: 'error',\n                message: 'No credits remaining. Purchase a credit pack to continue.',\n                action: 'buy-credits'\n            });\n        }\n        return suggestions;\n    };\n    const createCheckoutSession = async (tier)=>{\n        if (!user) {\n            throw new Error('User must be authenticated');\n        }\n        try {\n            const response = await fetch('/api/stripe/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    priceId: _lib_stripe__WEBPACK_IMPORTED_MODULE_2__.PRICING_TIERS[tier].priceId,\n                    userId: user.id,\n                    userEmail: user.email\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to create checkout session');\n            }\n            const { sessionId } = await response.json();\n            return sessionId;\n        } catch (err) {\n            console.error('Error creating checkout session:', err);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error('Failed to create checkout session');\n            throw err;\n        }\n    };\n    const cancelSubscription = async ()=>{\n        // For credit-based system, this would be removing/refunding credits\n        // For now, just show a message\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error('Credit packs cannot be cancelled. Credits are valid for 6 months.');\n        throw new Error('Credit packs cannot be cancelled');\n    };\n    const refreshSubscription = async ()=>{\n        await fetchCreditData();\n    };\n    // Create a mock subscription object for backward compatibility\n    const subscription = userCredits ? {\n        id: userCredits.id,\n        user_id: userCredits.user_id,\n        stripe_subscription_id: '',\n        stripe_customer_id: '',\n        status: userCredits.available_credits > 0 ? 'active' : 'inactive',\n        tier: 'pro',\n        current_period_start: userCredits.created_at,\n        current_period_end: userCredits.last_purchase_date,\n        cancel_at_period_end: false,\n        created_at: userCredits.created_at,\n        updated_at: userCredits.updated_at\n    } : null;\n    return {\n        subscription,\n        usage,\n        loading,\n        error,\n        canPerformAction,\n        canExtractVideo,\n        getRemainingCredits,\n        getRemainingExtractions,\n        getUsageInfo,\n        shouldWarnAboutCredits,\n        getUsageSuggestions,\n        createCheckoutSession,\n        cancelSubscription,\n        refreshSubscription,\n        userCredits\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/hooks/useSubscription.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./components/ui/avatar.tsx":
/*!**********************************!*\
  !*** ./components/ui/avatar.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Avatar: () => (/* binding */ Avatar),\n/* harmony export */   AvatarFallback: () => (/* binding */ AvatarFallback),\n/* harmony export */   AvatarImage: () => (/* binding */ AvatarImage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-avatar */ \"@radix-ui/react-avatar\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(pages-dir-node)/./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__]);\n([_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst Avatar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/avatar.tsx\",\n        lineNumber: 10,\n        columnNumber: 3\n    }, undefined));\nAvatar.displayName = _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__.Root.displayName;\nconst AvatarImage = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__.Image, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"aspect-square h-full w-full\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/avatar.tsx\",\n        lineNumber: 25,\n        columnNumber: 3\n    }, undefined));\nAvatarImage.displayName = _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__.Image.displayName;\nconst AvatarFallback = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__.Fallback, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex h-full w-full items-center justify-center rounded-full bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/avatar.tsx\",\n        lineNumber: 37,\n        columnNumber: 3\n    }, undefined));\nAvatarFallback.displayName = _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__.Fallback.displayName;\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/ui/avatar.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-slot */ \"@radix-ui/react-slot\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! class-variance-authority */ \"class-variance-authority\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(pages-dir-node)/./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__, class_variance_authority__WEBPACK_IMPORTED_MODULE_3__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__]);\n([_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__, class_variance_authority__WEBPACK_IMPORTED_MODULE_3__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_3__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n            outline: \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2\",\n            sm: \"h-8 rounded-md px-3 text-xs\",\n            lg: \"h-10 rounded-md px-8\",\n            icon: \"h-9 w-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/button.tsx\",\n        lineNumber: 47,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(pages-dir-node)/./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_utils__WEBPACK_IMPORTED_MODULE_2__]);\n_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-xl border bg-card text-card-foreground shadow\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/card.tsx\",\n        lineNumber: 48,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/card.tsx\",\n        lineNumber: 60,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/card.tsx\",\n        lineNumber: 68,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/ui/dropdown-menu.tsx":
/*!*****************************************!*\
  !*** ./components/ui/dropdown-menu.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DropdownMenu: () => (/* binding */ DropdownMenu),\n/* harmony export */   DropdownMenuCheckboxItem: () => (/* binding */ DropdownMenuCheckboxItem),\n/* harmony export */   DropdownMenuContent: () => (/* binding */ DropdownMenuContent),\n/* harmony export */   DropdownMenuGroup: () => (/* binding */ DropdownMenuGroup),\n/* harmony export */   DropdownMenuItem: () => (/* binding */ DropdownMenuItem),\n/* harmony export */   DropdownMenuLabel: () => (/* binding */ DropdownMenuLabel),\n/* harmony export */   DropdownMenuPortal: () => (/* binding */ DropdownMenuPortal),\n/* harmony export */   DropdownMenuRadioGroup: () => (/* binding */ DropdownMenuRadioGroup),\n/* harmony export */   DropdownMenuRadioItem: () => (/* binding */ DropdownMenuRadioItem),\n/* harmony export */   DropdownMenuSeparator: () => (/* binding */ DropdownMenuSeparator),\n/* harmony export */   DropdownMenuShortcut: () => (/* binding */ DropdownMenuShortcut),\n/* harmony export */   DropdownMenuSub: () => (/* binding */ DropdownMenuSub),\n/* harmony export */   DropdownMenuSubContent: () => (/* binding */ DropdownMenuSubContent),\n/* harmony export */   DropdownMenuSubTrigger: () => (/* binding */ DropdownMenuSubTrigger),\n/* harmony export */   DropdownMenuTrigger: () => (/* binding */ DropdownMenuTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-dropdown-menu */ \"@radix-ui/react-dropdown-menu\");\n/* harmony import */ var _radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-icons */ \"@radix-ui/react-icons\");\n/* harmony import */ var _radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(pages-dir-node)/./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__]);\n([_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst DropdownMenu = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Root;\nconst DropdownMenuTrigger = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Trigger;\nconst DropdownMenuGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Group;\nconst DropdownMenuPortal = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Portal;\nconst DropdownMenuSub = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Sub;\nconst DropdownMenuRadioGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.RadioGroup;\nconst DropdownMenuSubTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.SubTrigger, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent\", inset && \"pl-8\", className),\n        ...props,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_3__.ChevronRightIcon, {\n                className: \"ml-auto h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/dropdown-menu.tsx\",\n                lineNumber: 39,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/dropdown-menu.tsx\",\n        lineNumber: 29,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSubTrigger.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.SubTrigger.displayName;\nconst DropdownMenuSubContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.SubContent, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/dropdown-menu.tsx\",\n        lineNumber: 49,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSubContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.SubContent.displayName;\nconst DropdownMenuContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, sideOffset = 4, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Portal, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Content, {\n            ref: ref,\n            sideOffset: sideOffset,\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md\", \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/dropdown-menu.tsx\",\n            lineNumber: 66,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/dropdown-menu.tsx\",\n        lineNumber: 65,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Content.displayName;\nconst DropdownMenuItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Item, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/dropdown-menu.tsx\",\n        lineNumber: 86,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Item.displayName;\nconst DropdownMenuCheckboxItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, checked, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.CheckboxItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        checked: checked,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_3__.CheckIcon, {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/dropdown-menu.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/dropdown-menu.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/dropdown-menu.tsx\",\n                lineNumber: 111,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/dropdown-menu.tsx\",\n        lineNumber: 102,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuCheckboxItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.CheckboxItem.displayName;\nconst DropdownMenuRadioItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.RadioItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_3__.DotFilledIcon, {\n                        className: \"h-4 w-4 fill-current\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/dropdown-menu.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/dropdown-menu.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/dropdown-menu.tsx\",\n                lineNumber: 134,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/dropdown-menu.tsx\",\n        lineNumber: 126,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuRadioItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.RadioItem.displayName;\nconst DropdownMenuLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Label, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"px-2 py-1.5 text-sm font-semibold\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/dropdown-menu.tsx\",\n        lineNumber: 150,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuLabel.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Label.displayName;\nconst DropdownMenuSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Separator, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"-mx-1 my-1 h-px bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/dropdown-menu.tsx\",\n        lineNumber: 166,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSeparator.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Separator.displayName;\nconst DropdownMenuShortcut = ({ className, ...props })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"ml-auto text-xs tracking-widest opacity-60\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/dropdown-menu.tsx\",\n        lineNumber: 179,\n        columnNumber: 5\n    }, undefined);\n};\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/ui/dropdown-menu.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./lib/stripe.ts":
/*!***********************!*\
  !*** ./lib/stripe.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PRICING_TIERS: () => (/* binding */ PRICING_TIERS),\n/* harmony export */   stripePromise: () => (/* binding */ stripePromise)\n/* harmony export */ });\n/* harmony import */ var _stripe_stripe_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @stripe/stripe-js */ \"@stripe/stripe-js\");\n/* harmony import */ var _stripe_stripe_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_stripe_stripe_js__WEBPACK_IMPORTED_MODULE_0__);\n\nconst stripePublishableKey = \"pk_test_51Rbz5DG3dq2cZKhWQAhsMvCKvlJ7qki27vhezUpnbMECmp53uG6V3JmEy7pQodTCBWYNKeP55X64ZIJxaXfkijrM00xhD0btGs\";\nif (!stripePublishableKey && \"undefined\" !== 'undefined') {}\nconst stripePromise = stripePublishableKey ? (0,_stripe_stripe_js__WEBPACK_IMPORTED_MODULE_0__.loadStripe)(stripePublishableKey) : null;\n// Pricing configuration - Credit-based system\n// Credit system: 1 credit = 1 API call\n// - Getting available languages = 1 credit (optional)\n// - Downloading subtitles = 1 credit\n// - Default: Direct English download = 1 credit\nconst PRICING_TIERS = {\n    starter: {\n        name: 'Starter',\n        price: 10,\n        priceId: \"price_1Rbz9CG3dq2cZKhWCnIVHUx6\" || 0,\n        features: [\n            '50 credits (~50 subtitle downloads)',\n            'VTT and TXT format downloads',\n            'Manual and Auto-generated caption support',\n            'English subtitle extraction',\n            'Multi-language support',\n            'Credits valid for 6 months'\n        ],\n        limits: {\n            creditsPerMonth: 50,\n            creditsPerAction: 1,\n            actionsPerExtraction: 1 // Direct download = 1 action (English default)\n        },\n        popular: false\n    },\n    pro: {\n        name: 'Pro',\n        price: 30,\n        priceId: \"price_1RbzCCG3dq2cZKhWS3MlXiCZ\" || 0,\n        features: [\n            '200 credits (~200 subtitle downloads)',\n            'VTT and TXT format downloads',\n            'Manual and Auto-generated caption support',\n            'English subtitle extraction',\n            'Multi-language support',\n            'Credits valid for 6 months'\n        ],\n        limits: {\n            creditsPerMonth: 200,\n            creditsPerAction: 1,\n            actionsPerExtraction: 1 // Direct download = 1 action\n        },\n        popular: true\n    },\n    creator: {\n        name: 'Creator',\n        price: 75,\n        priceId: \"price_1RbzDHG3dq2cZKhWID6C0aMY\" || 0,\n        features: [\n            '600 credits (~600 subtitle downloads)',\n            'VTT and TXT format downloads',\n            'Manual and Auto-generated caption support',\n            'English subtitle extraction',\n            'Multi-language support',\n            'Credits valid for 6 months'\n        ],\n        limits: {\n            creditsPerMonth: 600,\n            creditsPerAction: 1,\n            actionsPerExtraction: 1 // Direct download = 1 action\n        },\n        popular: false\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./lib/stripe.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServerSupabaseClient: () => (/* binding */ createServerSupabaseClient),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\nconst supabaseUrl = \"https://vsaxiialrhpqdlcpmetn.supabase.co\" || 0;\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZzYXhpaWFscmhwcWRsY3BtZXRuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwOTQ1NzAsImV4cCI6MjA2NTY3MDU3MH0.MDe8-SLF1-73hqcunFsgOEvDYek1ku7Ap_A1UNardA8\" || 0;\n// Only throw error in production runtime (not during build)\nif ((!supabaseUrl || !supabaseAnonKey || supabaseUrl.includes('placeholder')) && \"development\" === 'production' && 0) {}\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, {\n    auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true,\n        flowType: 'pkce'\n    }\n});\n// Server-side client for API routes (Node.js environment)\nconst createServerSupabaseClient = ()=>{\n    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    if (!serviceRoleKey) {\n        throw new Error('Missing SUPABASE_SERVICE_ROLE_KEY environment variable');\n    }\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://vsaxiialrhpqdlcpmetn.supabase.co\" || 0, serviceRoleKey, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./lib/supabase.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"clsx\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"tailwind-merge\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__]);\n([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL2xpYi91dGlscy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBNEM7QUFDSjtBQUVqQyxTQUFTRSxHQUFHLEdBQUdDLE1BQW9CO0lBQ3hDLE9BQU9GLHVEQUFPQSxDQUFDRCwwQ0FBSUEsQ0FBQ0c7QUFDdEIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9kaW5lc2hzL0RvY3VtZW50cy9EZXYvUHJvamVjdHMvWVRTdWJ0aXRsZUV4dHJhY3Rvci9ZVFN1YnRpdGxlRXh0cmFjdG9yL2xpYi91dGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB0eXBlIENsYXNzVmFsdWUsIGNsc3ggfSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./lib/utils.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"(pages-dir-node)/./node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(pages-dir-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(pages-dir-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"(pages-dir-node)/./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"(pages-dir-node)/./pages/_app.tsx\");\n/* harmony import */ var _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages/index.tsx */ \"(pages-dir-node)/./pages/index.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var _components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/contexts/AuthContext */ \"(pages-dir-node)/./components/contexts/AuthContext.tsx\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../styles/globals.css */ \"(pages-dir-node)/./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_3__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_hot_toast__WEBPACK_IMPORTED_MODULE_1__, _components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__]);\n([react_hot_toast__WEBPACK_IMPORTED_MODULE_1__, _components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/pages/_app.tsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n                position: \"top-right\",\n                toastOptions: {\n                    duration: 4000,\n                    style: {\n                        background: '#1e293b',\n                        color: '#f1f5f9',\n                        border: '1px solid #475569'\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/pages/_app.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/pages/_app.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL19hcHAudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQzBDO0FBQ3dCO0FBQ25DO0FBRWhCLFNBQVNFLElBQUksRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQVk7SUFDNUQscUJBQ0UsOERBQUNILDBFQUFZQTs7MEJBQ1gsOERBQUNFO2dCQUFXLEdBQUdDLFNBQVM7Ozs7OzswQkFDeEIsOERBQUNKLG9EQUFPQTtnQkFDTkssVUFBUztnQkFDVEMsY0FBYztvQkFDWkMsVUFBVTtvQkFDVkMsT0FBTzt3QkFDTEMsWUFBWTt3QkFDWkMsT0FBTzt3QkFDUEMsUUFBUTtvQkFDVjtnQkFDRjs7Ozs7Ozs7Ozs7O0FBSVIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9kaW5lc2hzL0RvY3VtZW50cy9EZXYvUHJvamVjdHMvWVRTdWJ0aXRsZUV4dHJhY3Rvci9ZVFN1YnRpdGxlRXh0cmFjdG9yL3BhZ2VzL19hcHAudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgQXBwUHJvcHMgfSBmcm9tICduZXh0L2FwcCc7XG5pbXBvcnQgeyBUb2FzdGVyIH0gZnJvbSAncmVhY3QtaG90LXRvYXN0JztcbmltcG9ydCB7IEF1dGhQcm92aWRlciB9IGZyb20gJy4uL2NvbXBvbmVudHMvY29udGV4dHMvQXV0aENvbnRleHQnO1xuaW1wb3J0ICcuLi9zdHlsZXMvZ2xvYmFscy5jc3MnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBcHAoeyBDb21wb25lbnQsIHBhZ2VQcm9wcyB9OiBBcHBQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxBdXRoUHJvdmlkZXI+XG4gICAgICA8Q29tcG9uZW50IHsuLi5wYWdlUHJvcHN9IC8+XG4gICAgICA8VG9hc3RlclxuICAgICAgICBwb3NpdGlvbj1cInRvcC1yaWdodFwiXG4gICAgICAgIHRvYXN0T3B0aW9ucz17e1xuICAgICAgICAgIGR1cmF0aW9uOiA0MDAwLFxuICAgICAgICAgIHN0eWxlOiB7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiAnIzFlMjkzYicsXG4gICAgICAgICAgICBjb2xvcjogJyNmMWY1ZjknLFxuICAgICAgICAgICAgYm9yZGVyOiAnMXB4IHNvbGlkICM0NzU1NjknLFxuICAgICAgICAgIH0sXG4gICAgICAgIH19XG4gICAgICAvPlxuICAgIDwvQXV0aFByb3ZpZGVyPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlRvYXN0ZXIiLCJBdXRoUHJvdmlkZXIiLCJBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiLCJwb3NpdGlvbiIsInRvYXN0T3B0aW9ucyIsImR1cmF0aW9uIiwic3R5bGUiLCJiYWNrZ3JvdW5kIiwiY29sb3IiLCJib3JkZXIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_app.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"(pages-dir-node)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-node)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/Header */ \"(pages-dir-node)/./components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/Footer */ \"(pages-dir-node)/./components/Footer.tsx\");\n/* harmony import */ var _components_LandingPage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/LandingPage */ \"(pages-dir-node)/./components/LandingPage.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_Header__WEBPACK_IMPORTED_MODULE_3__, _components_Footer__WEBPACK_IMPORTED_MODULE_4__, _components_LandingPage__WEBPACK_IMPORTED_MODULE_5__]);\n([_components_Header__WEBPACK_IMPORTED_MODULE_3__, _components_Footer__WEBPACK_IMPORTED_MODULE_4__, _components_LandingPage__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nfunction Home() {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleGetStarted = ()=>{\n        router.push('/extractor');\n    };\n    const handleNavigate = (view)=>{\n        router.push(`/${view}`);\n    };\n    const handleFeedback = ()=>{\n        if (false) {}\n    };\n    const handleTermsClick = ()=>{\n        router.push('/terms');\n    };\n    const handlePrivacyClick = ()=>{\n        router.push('/privacy');\n    };\n    const handleDisclaimerClick = ()=>{\n        router.push('/disclaimer');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Download YouTube Subtitles - Free YT Caption Extractor - DownloadYTSubtitles\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/pages/index.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Free YouTube subtitle downloader and transcript extractor. Download YT captions in VTT and TXT formats from auto-generated and manual subtitles.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/pages/index.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"keywords\",\n                        content: \"YouTube subtitles, download YouTube captions, YouTube transcript extractor, YT subtitles, video captions\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/pages/index.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/pages/index.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/pages/index.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/pages/index.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen font-sans\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        currentView: \"landing\",\n                        onNavigate: handleNavigate,\n                        onFeedback: handleFeedback\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/pages/index.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LandingPage__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        onGetStarted: handleGetStarted,\n                        onNavigateToPricing: ()=>handleNavigate('pricing')\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/pages/index.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        onTermsClick: handleTermsClick,\n                        onPrivacyClick: handlePrivacyClick,\n                        onDisclaimerClick: handleDisclaimerClick\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/pages/index.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/pages/index.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/index.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=ArrowRight,CheckCircle,Download,Eye,Link,Search!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!****************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArrowRight,CheckCircle,Download,Eye,Link,Search!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArrowRight: () => (/* reexport safe */ _icons_arrow_right_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   CheckCircle: () => (/* reexport safe */ _icons_circle_check_big_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Download: () => (/* reexport safe */ _icons_download_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Eye: () => (/* reexport safe */ _icons_eye_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   Link: () => (/* reexport safe */ _icons_link_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   Search: () => (/* reexport safe */ _icons_search_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_arrow_right_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/arrow-right.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _icons_circle_check_big_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/circle-check-big.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _icons_download_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/download.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _icons_eye_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/eye.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _icons_link_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/link.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _icons_search_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/search.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUFycm93UmlnaHQsQ2hlY2tDaXJjbGUsRG93bmxvYWQsRXllLExpbmssU2VhcmNoIT0hLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQzhEO0FBQ007QUFDWDtBQUNWO0FBQ0UiLCJzb3VyY2VzIjpbIi9Vc2Vycy9kaW5lc2hzL0RvY3VtZW50cy9EZXYvUHJvamVjdHMvWVRTdWJ0aXRsZUV4dHJhY3Rvci9ZVFN1YnRpdGxlRXh0cmFjdG9yL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBBcnJvd1JpZ2h0IH0gZnJvbSBcIi4vaWNvbnMvYXJyb3ctcmlnaHQuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDaGVja0NpcmNsZSB9IGZyb20gXCIuL2ljb25zL2NpcmNsZS1jaGVjay1iaWcuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBEb3dubG9hZCB9IGZyb20gXCIuL2ljb25zL2Rvd25sb2FkLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRXllIH0gZnJvbSBcIi4vaWNvbnMvZXllLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTGluayB9IGZyb20gXCIuL2ljb25zL2xpbmsuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTZWFyY2ggfSBmcm9tIFwiLi9pY29ucy9zZWFyY2guanNcIiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/__barrel_optimize__?names=ArrowRight,CheckCircle,Download,Eye,Link,Search!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=ArrowRight,Crown,Download,Globe,Play,Shield,Star,Users,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!***************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArrowRight,Crown,Download,Globe,Play,Shield,Star,Users,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArrowRight: () => (/* reexport safe */ _icons_arrow_right_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Crown: () => (/* reexport safe */ _icons_crown_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Download: () => (/* reexport safe */ _icons_download_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Globe: () => (/* reexport safe */ _icons_globe_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   Play: () => (/* reexport safe */ _icons_play_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   Shield: () => (/* reexport safe */ _icons_shield_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   Star: () => (/* reexport safe */ _icons_star_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   Users: () => (/* reexport safe */ _icons_users_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   Zap: () => (/* reexport safe */ _icons_zap_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_arrow_right_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/arrow-right.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _icons_crown_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/crown.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _icons_download_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/download.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _icons_globe_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/globe.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _icons_play_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/play.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _icons_shield_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/shield.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _icons_star_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./icons/star.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _icons_users_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./icons/users.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _icons_zap_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./icons/zap.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUFycm93UmlnaHQsQ3Jvd24sRG93bmxvYWQsR2xvYmUsUGxheSxTaGllbGQsU3RhcixVc2VycyxaYXAhPSEuL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDOEQ7QUFDWDtBQUNNO0FBQ047QUFDRjtBQUNJO0FBQ0o7QUFDRSIsInNvdXJjZXMiOlsiL1VzZXJzL2RpbmVzaHMvRG9jdW1lbnRzL0Rldi9Qcm9qZWN0cy9ZVFN1YnRpdGxlRXh0cmFjdG9yL1lUU3VidGl0bGVFeHRyYWN0b3Ivbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEFycm93UmlnaHQgfSBmcm9tIFwiLi9pY29ucy9hcnJvdy1yaWdodC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENyb3duIH0gZnJvbSBcIi4vaWNvbnMvY3Jvd24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBEb3dubG9hZCB9IGZyb20gXCIuL2ljb25zL2Rvd25sb2FkLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgR2xvYmUgfSBmcm9tIFwiLi9pY29ucy9nbG9iZS5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFBsYXkgfSBmcm9tIFwiLi9pY29ucy9wbGF5LmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU2hpZWxkIH0gZnJvbSBcIi4vaWNvbnMvc2hpZWxkLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU3RhciB9IGZyb20gXCIuL2ljb25zL3N0YXIuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBVc2VycyB9IGZyb20gXCIuL2ljb25zL3VzZXJzLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgWmFwIH0gZnJvbSBcIi4vaWNvbnMvemFwLmpzXCIiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/__barrel_optimize__?names=ArrowRight,Crown,Download,Globe,Play,Shield,Star,Users,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=ChevronDown,ChevronUp,Download,FileText,Globe,HelpCircle,Shield,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ChevronDown,ChevronUp,Download,FileText,Globe,HelpCircle,Shield,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChevronDown: () => (/* reexport safe */ _icons_chevron_down_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   ChevronUp: () => (/* reexport safe */ _icons_chevron_up_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Download: () => (/* reexport safe */ _icons_download_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   FileText: () => (/* reexport safe */ _icons_file_text_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   Globe: () => (/* reexport safe */ _icons_globe_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   HelpCircle: () => (/* reexport safe */ _icons_circle_help_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   Shield: () => (/* reexport safe */ _icons_shield_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   Zap: () => (/* reexport safe */ _icons_zap_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_chevron_down_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/chevron-down.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _icons_chevron_up_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/chevron-up.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _icons_download_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/download.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _icons_file_text_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/file-text.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _icons_globe_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/globe.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _icons_circle_help_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/circle-help.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var _icons_shield_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./icons/shield.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _icons_zap_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./icons/zap.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUNoZXZyb25Eb3duLENoZXZyb25VcCxEb3dubG9hZCxGaWxlVGV4dCxHbG9iZSxIZWxwQ2lyY2xlLFNoaWVsZCxaYXAhPSEuL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQ2dFO0FBQ0o7QUFDSDtBQUNDO0FBQ1A7QUFDVztBQUNUIiwic291cmNlcyI6WyIvVXNlcnMvZGluZXNocy9Eb2N1bWVudHMvRGV2L1Byb2plY3RzL1lUU3VidGl0bGVFeHRyYWN0b3IvWVRTdWJ0aXRsZUV4dHJhY3Rvci9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2hldnJvbkRvd24gfSBmcm9tIFwiLi9pY29ucy9jaGV2cm9uLWRvd24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDaGV2cm9uVXAgfSBmcm9tIFwiLi9pY29ucy9jaGV2cm9uLXVwLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRG93bmxvYWQgfSBmcm9tIFwiLi9pY29ucy9kb3dubG9hZC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEZpbGVUZXh0IH0gZnJvbSBcIi4vaWNvbnMvZmlsZS10ZXh0LmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgR2xvYmUgfSBmcm9tIFwiLi9pY29ucy9nbG9iZS5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEhlbHBDaXJjbGUgfSBmcm9tIFwiLi9pY29ucy9jaXJjbGUtaGVscC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFNoaWVsZCB9IGZyb20gXCIuL2ljb25zL3NoaWVsZC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFphcCB9IGZyb20gXCIuL2ljb25zL3phcC5qc1wiIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/__barrel_optimize__?names=ChevronDown,ChevronUp,Download,FileText,Globe,HelpCircle,Shield,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=CreditCard,Crown,LogOut,Settings,User!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!******************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=CreditCard,Crown,LogOut,Settings,User!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CreditCard: () => (/* reexport safe */ _icons_credit_card_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Crown: () => (/* reexport safe */ _icons_crown_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   LogOut: () => (/* reexport safe */ _icons_log_out_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Settings: () => (/* reexport safe */ _icons_settings_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   User: () => (/* reexport safe */ _icons_user_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_credit_card_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/credit-card.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _icons_crown_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/crown.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _icons_log_out_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/log-out.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _icons_settings_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/settings.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _icons_user_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/user.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUNyZWRpdENhcmQsQ3Jvd24sTG9nT3V0LFNldHRpbmdzLFVzZXIhPSEuL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQzhEO0FBQ1g7QUFDRztBQUNHIiwic291cmNlcyI6WyIvVXNlcnMvZGluZXNocy9Eb2N1bWVudHMvRGV2L1Byb2plY3RzL1lUU3VidGl0bGVFeHRyYWN0b3IvWVRTdWJ0aXRsZUV4dHJhY3Rvci9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ3JlZGl0Q2FyZCB9IGZyb20gXCIuL2ljb25zL2NyZWRpdC1jYXJkLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ3Jvd24gfSBmcm9tIFwiLi9pY29ucy9jcm93bi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIExvZ091dCB9IGZyb20gXCIuL2ljb25zL2xvZy1vdXQuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTZXR0aW5ncyB9IGZyb20gXCIuL2ljb25zL3NldHRpbmdzLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgVXNlciB9IGZyb20gXCIuL2ljb25zL3VzZXIuanNcIiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/__barrel_optimize__?names=CreditCard,Crown,LogOut,Settings,User!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=CreditCard,Download,HelpCircle,Home,MessageSquare!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!******************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=CreditCard,Download,HelpCircle,Home,MessageSquare!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \******************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CreditCard: () => (/* reexport safe */ _icons_credit_card_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Download: () => (/* reexport safe */ _icons_download_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   HelpCircle: () => (/* reexport safe */ _icons_circle_help_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Home: () => (/* reexport safe */ _icons_house_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   MessageSquare: () => (/* reexport safe */ _icons_message_square_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_credit_card_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/credit-card.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _icons_download_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/download.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _icons_circle_help_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/circle-help.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var _icons_house_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/house.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _icons_message_square_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/message-square.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUNyZWRpdENhcmQsRG93bmxvYWQsSGVscENpcmNsZSxIb21lLE1lc3NhZ2VTcXVhcmUhPSEuL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQzhEO0FBQ0w7QUFDSztBQUNaIiwic291cmNlcyI6WyIvVXNlcnMvZGluZXNocy9Eb2N1bWVudHMvRGV2L1Byb2plY3RzL1lUU3VidGl0bGVFeHRyYWN0b3IvWVRTdWJ0aXRsZUV4dHJhY3Rvci9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ3JlZGl0Q2FyZCB9IGZyb20gXCIuL2ljb25zL2NyZWRpdC1jYXJkLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRG93bmxvYWQgfSBmcm9tIFwiLi9pY29ucy9kb3dubG9hZC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEhlbHBDaXJjbGUgfSBmcm9tIFwiLi9pY29ucy9jaXJjbGUtaGVscC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEhvbWUgfSBmcm9tIFwiLi9pY29ucy9ob3VzZS5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIE1lc3NhZ2VTcXVhcmUgfSBmcm9tIFwiLi9pY29ucy9tZXNzYWdlLXNxdWFyZS5qc1wiIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/__barrel_optimize__?names=CreditCard,Download,HelpCircle,Home,MessageSquare!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=FileText,Heart,Mail,Shield!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!*******************************************************************************************************************!*\
  !*** __barrel_optimize__?names=FileText,Heart,Mail,Shield!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FileText: () => (/* reexport safe */ _icons_file_text_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Heart: () => (/* reexport safe */ _icons_heart_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Mail: () => (/* reexport safe */ _icons_mail_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Shield: () => (/* reexport safe */ _icons_shield_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_file_text_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/file-text.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _icons_heart_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/heart.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _icons_mail_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/mail.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _icons_shield_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/shield.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUZpbGVUZXh0LEhlYXJ0LE1haWwsU2hpZWxkIT0hLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFDMEQ7QUFDUDtBQUNGIiwic291cmNlcyI6WyIvVXNlcnMvZGluZXNocy9Eb2N1bWVudHMvRGV2L1Byb2plY3RzL1lUU3VidGl0bGVFeHRyYWN0b3IvWVRTdWJ0aXRsZUV4dHJhY3Rvci9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRmlsZVRleHQgfSBmcm9tIFwiLi9pY29ucy9maWxlLXRleHQuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBIZWFydCB9IGZyb20gXCIuL2ljb25zL2hlYXJ0LmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTWFpbCB9IGZyb20gXCIuL2ljb25zL21haWwuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTaGllbGQgfSBmcm9tIFwiLi9pY29ucy9zaGllbGQuanNcIiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/__barrel_optimize__?names=FileText,Heart,Mail,Shield!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=Loader2,LogIn!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!******************************************************************************************************!*\
  !*** __barrel_optimize__?names=Loader2,LogIn!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Loader2: () => (/* reexport safe */ _icons_loader_circle_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   LogIn: () => (/* reexport safe */ _icons_log_in_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_loader_circle_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/loader-circle.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _icons_log_in_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/log-in.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUxvYWRlcjIsTG9nSW4hPSEuL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQzZEIiwic291cmNlcyI6WyIvVXNlcnMvZGluZXNocy9Eb2N1bWVudHMvRGV2L1Byb2plY3RzL1lUU3VidGl0bGVFeHRyYWN0b3IvWVRTdWJ0aXRsZUV4dHJhY3Rvci9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTG9hZGVyMiB9IGZyb20gXCIuL2ljb25zL2xvYWRlci1jaXJjbGUuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBMb2dJbiB9IGZyb20gXCIuL2ljb25zL2xvZy1pbi5qc1wiIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/__barrel_optimize__?names=Loader2,LogIn!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "@radix-ui/react-avatar":
/*!*****************************************!*\
  !*** external "@radix-ui/react-avatar" ***!
  \*****************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@radix-ui/react-avatar");;

/***/ }),

/***/ "@radix-ui/react-dropdown-menu":
/*!************************************************!*\
  !*** external "@radix-ui/react-dropdown-menu" ***!
  \************************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@radix-ui/react-dropdown-menu");;

/***/ }),

/***/ "@radix-ui/react-icons":
/*!****************************************!*\
  !*** external "@radix-ui/react-icons" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@radix-ui/react-icons");

/***/ }),

/***/ "@radix-ui/react-slot":
/*!***************************************!*\
  !*** external "@radix-ui/react-slot" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@radix-ui/react-slot");;

/***/ }),

/***/ "@stripe/stripe-js":
/*!************************************!*\
  !*** external "@stripe/stripe-js" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@stripe/stripe-js");

/***/ }),

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "class-variance-authority":
/*!*******************************************!*\
  !*** external "class-variance-authority" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = import("class-variance-authority");;

/***/ }),

/***/ "clsx":
/*!***********************!*\
  !*** external "clsx" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = import("clsx");;

/***/ }),

/***/ "framer-motion":
/*!********************************!*\
  !*** external "framer-motion" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = import("framer-motion");;

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react-hot-toast":
/*!**********************************!*\
  !*** external "react-hot-toast" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-hot-toast");;

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tailwind-merge":
/*!*********************************!*\
  !*** external "tailwind-merge" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("tailwind-merge");;

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();