import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { PRICING_TIERS, type PricingTier } from '@/lib/stripe';
import { useAuth } from '@/components/contexts/AuthContext';
import toast from 'react-hot-toast';

interface UserCredits {
  id: string;
  user_id: string;
  total_credits: number;
  used_credits: number;
  available_credits: number;
  last_purchase_date: string;
  created_at: string;
  updated_at: string;
}

interface UsageStats {
  id: string;
  user_id: string;
  credits_used_this_month: number;
  videos_extracted_this_month: number;
  last_reset_date: string;
  created_at: string;
  updated_at: string;
}

export const useSubscription = () => {
  const { user } = useAuth();
  const [userCredits, setUserCredits] = useState<UserCredits | null>(null);
  const [usage, setUsage] = useState<UsageStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      fetchCreditData();
    } else {
      setUserCredits(null);
      setUsage(null);
      setLoading(false);
    }
  }, [user]);

  const fetchCreditData = async () => {
    if (!user) return;

    try {
      setLoading(true);
      setError(null);

      // Get auth token for API request
      const { data: { session } } = await supabase.auth.getSession();
      const authHeaders = session?.access_token
        ? { 'Authorization': `Bearer ${session.access_token}` }
        : {};

      const response = await fetch('/api/user/credits', {
        headers: {
          'Content-Type': 'application/json',
          ...authHeaders
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch credit data');
      }

      const data = await response.json();
      setUserCredits(data.userCredits as UserCredits);
      setUsage(data.usage as UsageStats);
    } catch (err) {
      console.error('Error fetching credit data:', err);
      setError('Failed to fetch credit data');
    } finally {
      setLoading(false);
    }
  };

  const canPerformAction = (creditsNeeded: number = 1): boolean => {
    if (!userCredits) {
      return false;
    }

    // Check if user has enough available credits
    return userCredits.available_credits >= creditsNeeded;
  };

  // Backward compatibility - check if user can extract a complete video (1 credit now)
  const canExtractVideo = (): boolean => {
    return canPerformAction(1); // Direct download = 1 credit
  };

  const getRemainingCredits = (): number => {
    if (!userCredits) {
      return 0;
    }

    return userCredits.available_credits;
  };

  // Backward compatibility - get remaining complete extractions
  const getRemainingExtractions = (): number => {
    const remainingCredits = getRemainingCredits();
    return remainingCredits; // Each extraction needs 1 credit now
  };

  // New: Get simple usage information
  const getUsageInfo = () => {
    if (!userCredits) {
      return null;
    }

    return {
      creditsPerAction: 1, // 1 credit per action in new system
      actionsPerExtraction: 1 // 1 action per extraction
    };
  };

  // New: Check if user should be warned about credit usage
  const shouldWarnAboutCredits = (creditsNeeded: number = 1): boolean => {
    const remainingCredits = getRemainingCredits();
    const warningThreshold = 10; // Warn when less than 10 credits remaining

    return remainingCredits <= warningThreshold || remainingCredits < creditsNeeded;
  };

  // New: Get smart usage suggestions
  const getUsageSuggestions = () => {
    const remainingCredits = getRemainingCredits();
    const suggestions = [];

    if (remainingCredits <= 5) {
      suggestions.push({
        type: 'warning',
        message: 'Low credits remaining. Consider purchasing more credits.',
        action: 'upgrade'
      });
    }

    if (remainingCredits <= 2) {
      suggestions.push({
        type: 'error',
        message: 'Very low credits. Use English default to save credits.',
        action: 'use-english-default'
      });
    }

    if (remainingCredits === 0) {
      suggestions.push({
        type: 'error',
        message: 'No credits remaining. Purchase a credit pack to continue.',
        action: 'buy-credits'
      });
    }

    return suggestions;
  };

  const createCheckoutSession = async (tier: PricingTier): Promise<string> => {
    if (!user) {
      throw new Error('User must be authenticated');
    }

    try {
      const response = await fetch('/api/stripe/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          priceId: PRICING_TIERS[tier].priceId,
          userId: user.id,
          userEmail: user.email
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create checkout session');
      }

      const { sessionId } = await response.json();
      return sessionId;
    } catch (err) {
      console.error('Error creating checkout session:', err);
      toast.error('Failed to create checkout session');
      throw err;
    }
  };

  const cancelSubscription = async (): Promise<void> => {
    // For credit-based system, this would be removing/refunding credits
    // For now, just show a message
    toast.error('Credit packs cannot be cancelled. Credits are valid for 6 months.');
    throw new Error('Credit packs cannot be cancelled');
  };

  const refreshSubscription = async (): Promise<void> => {
    await fetchCreditData();
  };

  // Create a mock subscription object for backward compatibility
  const subscription = userCredits ? {
    id: userCredits.id,
    user_id: userCredits.user_id,
    stripe_subscription_id: '',
    stripe_customer_id: '',
    status: userCredits.available_credits > 0 ? 'active' : 'inactive',
    tier: 'pro', // Default tier for credit users
    current_period_start: userCredits.created_at,
    current_period_end: userCredits.last_purchase_date,
    cancel_at_period_end: false,
    created_at: userCredits.created_at,
    updated_at: userCredits.updated_at
  } : null;

  return {
    subscription,
    usage,
    loading,
    error,
    canPerformAction,
    canExtractVideo,
    getRemainingCredits,
    getRemainingExtractions,
    getUsageInfo,
    shouldWarnAboutCredits,
    getUsageSuggestions,
    createCheckoutSession,
    cancelSubscription,
    refreshSubscription,
    userCredits // Add this for direct access to credit data
  };
};
