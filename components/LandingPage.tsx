import { motion } from 'framer-motion';
import { Play, Download, Globe, Zap, Shield, Users, Crown, Star, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import HowToUse from './HowToUse';
import FAQ from './FAQ';
import { PRICING_TIERS } from '@/lib/stripe';

interface LandingPageProps {
  onGetStarted: () => void;
  onNavigateToPricing?: () => void;
}

const LandingPage = ({ onGetStarted, onNavigateToPricing }: LandingPageProps) => {
  const features = [
    {
      icon: <Play className="w-8 h-8" />,
      title: "YouTube Video Support",
      description: "Download subtitles from any YouTube video with available captions or auto-generated transcripts."
    },
    {
      icon: <Globe className="w-8 h-8" />,
      title: "70+ Languages Supported",
      description: "Extract YouTube captions in multiple languages including English, Spanish, French, German, and more."
    },
    {
      icon: <Zap className="w-8 h-8" />,
      title: "Auto-Generated Captions",
      description: "Advanced processing of YouTube's AI-generated subtitles with automatic cleaning and formatting."
    },
    {
      icon: <Download className="w-8 h-8" />,
      title: "VTT & TXT Formats",
      description: "Download YouTube subtitles in VTT format for video players and web, or TXT with metadata for reading."
    },
    {
      icon: <Shield className="w-8 h-8" />,
      title: "Privacy Protected",
      description: "No data stored on our servers. YouTube subtitle extraction happens in real-time without tracking."
    },
    {
      icon: <Users className="w-8 h-8" />,
      title: "Professional Plans",
      description: "Choose from flexible pricing plans designed for individuals, teams, and businesses of all sizes."
    }
  ];

  const useCases = [
    {
      title: "Content Creators",
      description: "Extract subtitles for video editing, translation, or accessibility compliance.",
      gradient: "from-purple-500 to-pink-500"
    },
    {
      title: "Students & Researchers",
      description: "Get transcripts from educational videos for note-taking and research.",
      gradient: "from-blue-500 to-cyan-500"
    },
    {
      title: "Language Learners",
      description: "Study foreign languages with accurate subtitles and translations.",
      gradient: "from-green-500 to-emerald-500"
    },
    {
      title: "Accessibility Teams",
      description: "Create accessible content with properly formatted subtitle files.",
      gradient: "from-orange-500 to-red-500"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-800/20 to-pink-800/20" />
        <div className="relative max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 pt-12 sm:pt-20 pb-12 sm:pb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="text-3xl sm:text-5xl md:text-7xl font-bold text-white mb-4 sm:mb-6 leading-tight"
            >
              Download YouTube
              <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                {" "}Subtitles
              </span>
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="text-base sm:text-xl md:text-2xl text-gray-300 mb-6 sm:mb-8 max-w-3xl mx-auto px-2"
            >
              Professional YouTube subtitle downloader and transcript extractor. Get YT captions in VTT and TXT formats.
              Export YouTube transcripts from auto-generated and manual subtitles in 70+ languages.
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center"
            >
              <Button
                onClick={onGetStarted}
                size="lg"
                className="w-full sm:w-auto bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <Play className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
                Get Started
              </Button>

              <Button
                variant="outline"
                size="lg"
                className="w-full sm:w-auto border-purple-400 text-purple-400 hover:bg-purple-400 hover:text-white px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg font-semibold rounded-full transition-all duration-300"
                onClick={() => {
                  if (typeof window !== 'undefined') {
                    document.getElementById('features')?.scrollIntoView({ behavior: 'smooth' });
                  }
                }}
              >
                Learn More
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-12 sm:py-20 bg-slate-800/50">
        <div className="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-12 sm:mb-16"
          >
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-3 sm:mb-4">
              Powerful Features
            </h2>
            <p className="text-lg sm:text-xl text-gray-300 max-w-2xl mx-auto px-2">
              Everything you need to extract and process YouTube subtitles professionally
            </p>
          </motion.div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ scale: 1.02 }}
                className="bg-slate-800/80 backdrop-blur-sm rounded-xl p-4 sm:p-6 border border-slate-700 hover:border-purple-500 transition-all duration-300"
              >
                <div className="text-purple-400 mb-3 sm:mb-4">
                  {feature.icon}
                </div>
                <h3 className="text-lg sm:text-xl font-semibold text-white mb-2">
                  {feature.title}
                </h3>
                <p className="text-sm sm:text-base text-gray-300">
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="py-12 sm:py-20">
        <div className="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-12 sm:mb-16"
          >
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-3 sm:mb-4">
              Perfect For Everyone
            </h2>
            <p className="text-lg sm:text-xl text-gray-300 max-w-2xl mx-auto px-2">
              From content creators to researchers, our tool serves diverse needs
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-8">
            {useCases.map((useCase, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="relative overflow-hidden rounded-xl bg-slate-800/80 backdrop-blur-sm border border-slate-700 p-4 sm:p-8 hover:border-purple-500 transition-all duration-300"
              >
                <div className={`absolute inset-0 bg-gradient-to-r ${useCase.gradient} opacity-10`} />
                <div className="relative">
                  <h3 className="text-xl sm:text-2xl font-bold text-white mb-3 sm:mb-4">
                    {useCase.title}
                  </h3>
                  <p className="text-gray-300 text-base sm:text-lg">
                    {useCase.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* How to Use Section */}
      <HowToUse />

      {/* FAQ Section */}
      <section id="faq" className="py-12 sm:py-20 bg-slate-800/30">
        <div className="max-w-4xl mx-auto px-3 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-12 sm:mb-16"
          >
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-3 sm:mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-lg sm:text-xl text-gray-300 max-w-2xl mx-auto px-2">
              Everything you need to know about downloading YouTube subtitles
            </p>
          </motion.div>
          <FAQ showHeader={false} />
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-12 sm:py-20 bg-slate-800/50">
        <div className="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-4 sm:mb-6">
              Choose Your Plan
            </h2>
            <p className="text-lg sm:text-xl text-gray-300 max-w-3xl mx-auto">
              Professional subtitle extraction with flexible pricing for every need
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 sm:gap-8 mb-12">
            {Object.entries(PRICING_TIERS).map(([key, tier], index) => (
              <motion.div
                key={key}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="relative"
              >
                <Card className={`relative overflow-hidden ${tier.popular ? 'border-purple-500 shadow-lg shadow-purple-500/20 scale-105' : 'border-slate-700'} bg-slate-800/80 backdrop-blur-sm h-full`}>
                  {tier.popular && (
                    <div className="absolute top-0 left-0 right-0">
                      <div className="bg-gradient-to-r from-purple-500 to-pink-500 text-white text-center py-2 text-sm font-medium">
                        Most Popular
                      </div>
                    </div>
                  )}

                  <CardHeader className={tier.popular ? 'pt-12' : 'pt-6'}>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-white flex items-center gap-2">
                        {key === 'starter' && <Zap className="w-5 h-5 text-blue-500" />}
                        {key === 'pro' && <Star className="w-5 h-5 text-purple-500" />}
                        {key === 'premium' && <Crown className="w-5 h-5 text-yellow-500" />}
                        {tier.name}
                      </CardTitle>
                    </div>
                    <CardDescription className="text-gray-300">
                      <div className="flex items-baseline gap-1">
                        <span className="text-3xl font-bold text-white">${tier.price}</span>
                        <span className="text-gray-400">/month</span>
                      </div>
                    </CardDescription>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    <ul className="space-y-2">
                      {tier.features.slice(0, 4).map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-start gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                          <span className="text-gray-300">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="text-center"
          >
            <Button
              onClick={onNavigateToPricing}
              variant="outline"
              size="lg"
              className="border-purple-500 text-purple-400 hover:bg-purple-500 hover:text-white"
            >
              View All Plans & Features
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-12 sm:py-20 bg-gradient-to-r from-purple-800/20 to-pink-800/20">
        <div className="max-w-4xl mx-auto px-3 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-4 sm:mb-6">
              Ready to Extract Subtitles?
            </h2>
            <p className="text-lg sm:text-xl text-gray-300 mb-6 sm:mb-8 px-2">
              Start extracting professional-quality subtitles in seconds
            </p>
            <Button
              onClick={onGetStarted}
              size="lg"
              className="w-full sm:w-auto bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 sm:px-12 py-3 sm:py-4 text-lg sm:text-xl font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <Download className="w-5 h-5 sm:w-6 sm:h-6 mr-2" />
              Start Extracting Now
            </Button>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default LandingPage;
