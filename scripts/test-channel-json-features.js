#!/usr/bin/env node

/**
 * Test script to verify channel name extraction and JSON format support
 */

import youtubeDl from 'youtube-dl-exec';

async function testChannelAndJsonFeatures() {
  console.log('🧪 Testing Channel Name Extraction and JSON Format Support...\n');

  // Test video ID (<PERSON> - Never Gonna Give You Up)
  const testVideoId = 'dQw4w9WgXcQ';
  const videoUrl = `https://www.youtube.com/watch?v=${testVideoId}`;

  try {
    console.log(`📹 Testing with video: ${videoUrl}`);
    
    // Configure options similar to what we use in the API
    const options = {
      dumpSingleJson: true,
      noCheckCertificates: true,
      noWarnings: true,
      preferFreeFormats: true,
      addHeader: [
        'referer:youtube.com',
        'user-agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      ]
    };

    console.log('⏳ Fetching video information...');
    const videoInfo = await youtubeDl(videoUrl, options);

    console.log('✅ Successfully fetched video information!');
    
    // Test channel information extraction
    console.log('\n📺 Channel Information:');
    console.log(`   Title: ${videoInfo.title}`);
    console.log(`   Channel: ${videoInfo.uploader || videoInfo.channel || 'Not found'}`);
    console.log(`   Channel ID: ${videoInfo.uploader_id || videoInfo.channel_id || 'Not found'}`);
    console.log(`   Channel URL: ${videoInfo.uploader_url || videoInfo.channel_url || 'Not found'}`);
    console.log(`   Duration: ${videoInfo.duration} seconds`);
    console.log(`   Upload Date: ${videoInfo.upload_date || 'Not found'}`);
    console.log(`   View Count: ${videoInfo.view_count || 'Not found'}`);

    // Test subtitle extraction for JSON format
    console.log('\n🔍 Testing subtitle extraction...');
    
    let subtitles = [];
    let subtitleUrl = null;

    // Try to get English subtitles
    if (videoInfo.subtitles?.en) {
      const englishSubs = videoInfo.subtitles.en;
      const vttTrack = englishSubs.find(track => track.ext === 'vtt');
      if (vttTrack) {
        subtitleUrl = vttTrack.url;
        console.log('📄 Found manual English VTT subtitles');
      }
    }

    // Fallback to auto-generated if no manual subtitles
    if (!subtitleUrl && videoInfo.automatic_captions?.en) {
      const autoSubs = videoInfo.automatic_captions.en;
      const vttTrack = autoSubs.find(track => track.ext === 'vtt');
      if (vttTrack) {
        subtitleUrl = vttTrack.url;
        console.log('🤖 Found auto-generated English VTT subtitles');
      }
    }

    if (subtitleUrl) {
      console.log('⏳ Downloading subtitle content...');
      const response = await fetch(subtitleUrl);
      const content = await response.text();
      
      // Parse a few subtitles for testing
      const lines = content.split('\n');
      let sampleSubtitles = [];
      
      for (let i = 0; i < lines.length && sampleSubtitles.length < 3; i++) {
        const line = lines[i].trim();
        if (line.includes('-->')) {
          const timestampMatch = line.match(/^([\d:.,]+)\s*-->\s*([\d:.,]+)/);
          if (timestampMatch) {
            const start = parseVTTTime(timestampMatch[1]);
            const end = parseVTTTime(timestampMatch[2]);
            
            // Get text from next lines
            let text = '';
            i++;
            while (i < lines.length && !lines[i].includes('-->') && lines[i].trim() !== '') {
              const textLine = lines[i].trim();
              if (textLine && !textLine.includes('<c>') && !textLine.match(/<\d+:\d+:\d+\.\d+>/)) {
                text += textLine + ' ';
              }
              i++;
            }
            i--; // Step back
            
            if (text.trim()) {
              sampleSubtitles.push({
                start,
                end,
                text: text.trim()
              });
            }
          }
        }
      }

      console.log(`✅ Extracted ${sampleSubtitles.length} sample subtitles`);

      // Test JSON format generation
      console.log('\n📋 Testing JSON Format Generation:');
      
      const jsonData = {
        metadata: {
          title: videoInfo.title || 'YouTube Video',
          channel: videoInfo.uploader || videoInfo.channel || 'Unknown Channel',
          channelId: videoInfo.uploader_id || videoInfo.channel_id || null,
          channelUrl: videoInfo.uploader_url || videoInfo.channel_url || null,
          videoId: testVideoId,
          language: 'en',
          duration: videoInfo.duration || null,
          uploadDate: videoInfo.upload_date || null,
          viewCount: videoInfo.view_count || null,
          thumbnail: videoInfo.thumbnail || null,
          extractedAt: new Date().toISOString(),
          totalSubtitles: sampleSubtitles.length
        },
        subtitles: sampleSubtitles.map((subtitle, index) => ({
          index: index + 1,
          start: subtitle.start,
          end: subtitle.end,
          duration: subtitle.end - subtitle.start,
          text: subtitle.text,
          startTime: formatVTTTime(subtitle.start),
          endTime: formatVTTTime(subtitle.end)
        }))
      };

      console.log('📄 Sample JSON Output:');
      console.log('---');
      console.log(JSON.stringify(jsonData, null, 2));
      console.log('---');

      // Test format generation functions
      console.log('\n🔧 Testing Format Generation:');
      
      // VTT Format
      let vttContent = 'WEBVTT\nKind: captions\nLanguage: en\n\n';
      vttContent += `NOTE\nTitle: ${videoInfo.title}\n`;
      vttContent += `Channel: ${videoInfo.uploader || videoInfo.channel}\n`;
      vttContent += `Video ID: ${testVideoId}\n\n`;
      
      sampleSubtitles.forEach(sub => {
        vttContent += `${formatVTTTime(sub.start)} --> ${formatVTTTime(sub.end)}\n`;
        vttContent += `${sub.text}\n\n`;
      });
      
      console.log('✅ VTT format generated successfully');
      
      // TXT Format
      let txtContent = `Title: ${videoInfo.title}\n`;
      txtContent += `Channel: ${videoInfo.uploader || videoInfo.channel}\n`;
      txtContent += `Video ID: ${testVideoId}\n`;
      txtContent += `Language: en\n`;
      txtContent += `Duration: ${Math.floor(videoInfo.duration / 60)}:${String(videoInfo.duration % 60).padStart(2, '0')}\n\n`;
      txtContent += '--- Subtitles ---\n\n';
      
      sampleSubtitles.forEach(sub => {
        const startMin = Math.floor(sub.start / 60);
        const startSec = Math.floor(sub.start % 60);
        const endMin = Math.floor(sub.end / 60);
        const endSec = Math.floor(sub.end % 60);
        txtContent += `[${startMin}:${String(startSec).padStart(2, '0')} - ${endMin}:${String(endSec).padStart(2, '0')}] ${sub.text}\n`;
      });
      
      console.log('✅ TXT format generated successfully');
      console.log('✅ JSON format generated successfully');

    } else {
      console.log('⚠️  No English subtitles found for testing');
    }

    console.log('\n🎉 Channel and JSON features test completed successfully!');
    console.log('\n📋 Feature Summary:');
    console.log('   ✅ Channel name extraction working');
    console.log('   ✅ Channel metadata extraction working');
    console.log('   ✅ JSON format generation working');
    console.log('   ✅ Enhanced VTT format with metadata working');
    console.log('   ✅ Enhanced TXT format with metadata working');
    console.log('   ✅ Ready for production use');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error('\n🔧 Troubleshooting tips:');
    console.error('   1. Make sure yt-dlp is installed: pip install yt-dlp');
    console.error('   2. Check your internet connection');
    console.error('   3. Verify the test video is accessible');
    
    process.exit(1);
  }
}

// Helper functions
function parseVTTTime(timeStr) {
  const normalizedTime = timeStr.replace(',', '.');
  const parts = normalizedTime.split(':');
  
  if (parts.length === 3) {
    const hours = parseInt(parts[0]) || 0;
    const minutes = parseInt(parts[1]) || 0;
    const seconds = parseFloat(parts[2]) || 0;
    return hours * 3600 + minutes * 60 + seconds;
  }
  
  return 0;
}

function formatVTTTime(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  
  return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${secs.toFixed(3).padStart(6, '0')}`;
}

// Run the test
testChannelAndJsonFeatures().catch(console.error);
