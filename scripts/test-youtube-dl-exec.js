#!/usr/bin/env node

/**
 * Test script to verify youtube-dl-exec migration
 * This script tests basic functionality of youtube-dl-exec
 */

import youtubeDl from 'youtube-dl-exec';

async function testYoutubeDlExec() {
  console.log('🧪 Testing youtube-dl-exec migration...\n');

  // Test video ID (a short, stable YouTube video)
  const testVideoId = 'dQw4w9WgXcQ'; // <PERSON> - Never Gonna Give You Up
  const videoUrl = `https://www.youtube.com/watch?v=${testVideoId}`;

  try {
    console.log(`📹 Testing with video: ${videoUrl}`);
    
    // Configure options similar to what we use in the API
    const options = {
      dumpSingleJson: true,
      noCheckCertificates: true,
      noWarnings: true,
      preferFreeFormats: true,
      addHeader: [
        'referer:youtube.com',
        'user-agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      ]
    };

    console.log('⏳ Fetching video information...');
    const videoInfo = await youtubeDl(videoUrl, options);

    console.log('✅ Successfully fetched video information!');
    console.log(`📝 Title: ${videoInfo.title}`);
    console.log(`⏱️  Duration: ${videoInfo.duration} seconds`);
    console.log(`👤 Uploader: ${videoInfo.uploader}`);

    // Test subtitle availability
    console.log('\n🔍 Checking subtitle availability...');
    
    let totalLanguages = 0;
    let manualSubtitles = 0;
    let autoSubtitles = 0;

    if (videoInfo.subtitles) {
      manualSubtitles = Object.keys(videoInfo.subtitles).length;
      console.log(`📄 Manual subtitles available in ${manualSubtitles} languages:`);
      Object.keys(videoInfo.subtitles).slice(0, 5).forEach(lang => {
        console.log(`   - ${lang}`);
      });
      if (manualSubtitles > 5) {
        console.log(`   ... and ${manualSubtitles - 5} more`);
      }
    }

    if (videoInfo.automatic_captions) {
      autoSubtitles = Object.keys(videoInfo.automatic_captions).length;
      console.log(`🤖 Auto-generated subtitles available in ${autoSubtitles} languages:`);
      Object.keys(videoInfo.automatic_captions).slice(0, 5).forEach(lang => {
        console.log(`   - ${lang}`);
      });
      if (autoSubtitles > 5) {
        console.log(`   ... and ${autoSubtitles - 5} more`);
      }
    }

    totalLanguages = manualSubtitles + autoSubtitles;
    console.log(`\n📊 Total subtitle languages: ${totalLanguages}`);

    // Test English subtitle URL extraction
    if (videoInfo.subtitles?.en || videoInfo.automatic_captions?.en) {
      console.log('\n🇺🇸 Testing English subtitle extraction...');
      
      let englishSubtitles = videoInfo.subtitles?.en || videoInfo.automatic_captions?.en;
      let vttTrack = englishSubtitles.find(track => track.ext === 'vtt');
      
      if (vttTrack) {
        console.log(`✅ Found English VTT subtitles: ${vttTrack.url.substring(0, 100)}...`);
        
        // Test downloading a small portion of the subtitle
        try {
          const response = await fetch(vttTrack.url);
          const content = await response.text();
          const lines = content.split('\n').slice(0, 10);
          console.log('📝 Sample subtitle content:');
          lines.forEach(line => {
            if (line.trim()) {
              console.log(`   ${line}`);
            }
          });
        } catch (fetchError) {
          console.log('⚠️  Could not fetch subtitle content (this is normal in some environments)');
        }
      } else {
        console.log('⚠️  No VTT format found for English subtitles');
      }
    } else {
      console.log('⚠️  No English subtitles available for this video');
    }

    console.log('\n🎉 youtube-dl-exec migration test completed successfully!');
    console.log('\n📋 Migration Summary:');
    console.log('   ✅ youtube-dl-exec is working correctly');
    console.log('   ✅ Video information extraction works');
    console.log('   ✅ Subtitle detection works');
    console.log('   ✅ Ready for production use');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error('\n🔧 Troubleshooting tips:');
    console.error('   1. Make sure yt-dlp is installed: pip install yt-dlp');
    console.error('   2. Check your internet connection');
    console.error('   3. Verify the test video is accessible');
    console.error('   4. Check if you need proxy configuration');
    
    process.exit(1);
  }
}

// Run the test
testYoutubeDlExec().catch(console.error);
