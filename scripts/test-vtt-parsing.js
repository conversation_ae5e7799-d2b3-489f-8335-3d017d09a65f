#!/usr/bin/env node

/**
 * Test script to verify VTT parsing with word-level timing tags
 */

// Parse VTT time format to seconds
const parseVTTTime = (timeStr) => {
  // Handle both comma and dot as decimal separator
  const normalizedTime = timeStr.replace(',', '.');
  const parts = normalizedTime.split(':');
  
  if (parts.length === 3) {
    const hours = parseInt(parts[0]) || 0;
    const minutes = parseInt(parts[1]) || 0;
    const seconds = parseFloat(parts[2]) || 0;
    return hours * 3600 + minutes * 60 + seconds;
  } else if (parts.length === 2) {
    // Handle MM:SS.mmm format
    const minutes = parseInt(parts[0]) || 0;
    const seconds = parseFloat(parts[1]) || 0;
    return minutes * 60 + seconds;
  }
  
  return 0;
};

// Parse VTT subtitle format
const parseVTTContent = (content) => {
  const subtitles = [];
  const lines = content.split('\n');
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    
    // Look for timestamp lines (format: 00:00:00.000 --> 00:00:03.000)
    if (line.includes('-->')) {
      // Extract timestamp part (before any alignment/position attributes)
      const timestampMatch = line.match(/^([\d:.,]+)\s*-->\s*([\d:.,]+)/);
      if (!timestampMatch) continue;
      
      const startTime = timestampMatch[1];
      const endTime = timestampMatch[2];
      const start = parseVTTTime(startTime);
      const end = parseVTTTime(endTime);
      
      // Get the text lines that follow
      const textLines = [];
      const wordTimingLines = [];
      i++; // Move to next line after timestamp

      while (i < lines.length && !lines[i].includes('-->')) {
        const textLine = lines[i].trim();

        // Break on empty line (indicates end of this subtitle block)
        if (textLine === '') {
          break;
        }

        if (textLine) {
          // Check if this line contains word-level timing tags
          const hasWordTiming = textLine.includes('<c>') || textLine.match(/<\d+:\d+:\d+\.\d+>/);

          if (hasWordTiming) {
            // Store word-timing lines for potential extraction
            wordTimingLines.push(textLine);
          } else {
            // This is a clean text line, process it
            let cleanText = textLine
              // Remove any remaining VTT formatting tags
              .replace(/<[^>]*>/g, '')
              // Clean up extra spaces
              .replace(/\s+/g, ' ')
              .trim();

            if (cleanText && cleanText.length > 0) {
              textLines.push(cleanText);
            }
          }
        }
        i++;
      }
      i--; // Step back one since the loop will increment

      // If no clean text lines found, extract text from word-timing lines
      if (textLines.length === 0 && wordTimingLines.length > 0) {
        wordTimingLines.forEach(line => {
          let cleanText = line
            // Remove word-level timing tags like <00:00:01.920>
            .replace(/<\d+:\d+:\d+\.\d+>/g, '')
            // Remove <c> tags and other formatting
            .replace(/<[^>]*>/g, '')
            // Clean up extra spaces
            .replace(/\s+/g, ' ')
            .trim();

          if (cleanText && cleanText.length > 0) {
            textLines.push(cleanText);
          }
        });
      }
      
      // Only add subtitle if we have meaningful text and reasonable duration
      const duration = end - start;
      const finalText = textLines.join(' ').trim();

      if (finalText && finalText.length > 0 && duration > 0.1 && duration < 30) {
        subtitles.push({
          start,
          end,
          text: finalText
        });
      }
    }
  }
  
  // Merge consecutive subtitles with very short gaps (less than 0.2 seconds) and similar text
  const mergedSubtitles = [];
  for (let i = 0; i < subtitles.length; i++) {
    const current = subtitles[i];
    const next = subtitles[i + 1];

    // Only merge if gap is very small and texts are different (avoid duplicates)
    if (next &&
        (next.start - current.end) < 0.2 &&
        current.text &&
        next.text &&
        current.text !== next.text &&
        !next.text.includes(current.text)) {
      // Merge with next subtitle
      mergedSubtitles.push({
        start: current.start,
        end: next.end,
        text: `${current.text} ${next.text}`.trim()
      });
      i++; // Skip the next subtitle since we merged it
    } else {
      mergedSubtitles.push(current);
    }
  }

  return mergedSubtitles;
};

// Test VTT content with word-level timing
const testVTTContent = `WEBVTT
Kind: captions
Language: en

00:00:01.800 --> 00:00:06.630 align:start position:0%
this<00:00:01.920><c> is</c><00:00:02.120><c> really</c><00:00:02.360><c> delicious</c><00:00:02.840><c> isn't</c>

00:00:06.630 --> 00:00:06.640 align:start position:0%
 
 

00:00:06.640 --> 00:00:08.950 align:start position:0%
 
it<00:00:07.640><c> still</c><00:00:08.000><c> can't</c><00:00:08.160><c> talk</c><00:00:08.320><c> to</c><00:00:08.440><c> me</c><00:00:08.559><c> unless</c><00:00:08.800><c> you're</c>

00:00:08.950 --> 00:00:08.960 align:start position:0%
it still can't talk to me unless you're
 

00:00:08.960 --> 00:00:12.400 align:start position:0%
it still can't talk to me unless you're
going<00:00:09.200><c> to</c><00:00:09.320><c> help</c><00:00:09.480><c> me</c><00:00:09.640><c> with</c><00:00:09.800><c> something</c>

00:00:12.400 --> 00:00:15.200 align:start position:0%
going to help me with something
important<00:00:13.000><c> today</c>

00:00:15.200 --> 00:00:18.500 align:start position:0%
important today

00:00:18.500 --> 00:00:22.000 align:start position:0%
This<00:00:18.600><c> is</c><00:00:18.800><c> a</c><00:00:19.000><c> normal</c><00:00:19.400><c> sentence</c><00:00:20.000><c> with</c><00:00:20.200><c> timing</c>

00:00:22.000 --> 00:00:25.000 align:start position:0%
This is a normal sentence with timing`;

function testVTTParsing() {
  console.log('🧪 Testing VTT parsing with word-level timing tags...\n');
  
  console.log('📝 Input VTT content:');
  console.log('---');
  console.log(testVTTContent);
  console.log('---\n');
  
  const parsedSubtitles = parseVTTContent(testVTTContent);
  
  console.log('✅ Parsed subtitles:');
  console.log('---');
  parsedSubtitles.forEach((subtitle, index) => {
    const startTime = new Date(subtitle.start * 1000).toISOString().substr(11, 12);
    const endTime = new Date(subtitle.end * 1000).toISOString().substr(11, 12);
    console.log(`${index + 1}. [${startTime} --> ${endTime}] "${subtitle.text}"`);
  });
  console.log('---\n');
  
  console.log('📊 Results:');
  console.log(`   • Total subtitles: ${parsedSubtitles.length}`);
  console.log(`   • Word-level timing tags removed: ✅`);
  console.log(`   • Empty subtitles filtered: ✅`);
  console.log(`   • Clean text extracted: ✅`);
  
  // Verify specific expectations
  const hasCleanText = parsedSubtitles.every(sub => 
    !sub.text.includes('<c>') && 
    !sub.text.includes('<00:') &&
    sub.text.trim().length > 0
  );
  
  const hasReasonableDurations = parsedSubtitles.every(sub => 
    (sub.end - sub.start) > 0.1 && (sub.end - sub.start) < 30
  );
  
  console.log(`   • All text is clean: ${hasCleanText ? '✅' : '❌'}`);
  console.log(`   • All durations reasonable: ${hasReasonableDurations ? '✅' : '❌'}`);
  
  if (hasCleanText && hasReasonableDurations && parsedSubtitles.length > 0) {
    console.log('\n🎉 VTT parsing test passed! Ready for production.');
  } else {
    console.log('\n❌ VTT parsing test failed. Check the implementation.');
    process.exit(1);
  }
}

testVTTParsing();
